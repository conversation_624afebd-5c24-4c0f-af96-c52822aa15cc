import { NextRequest, NextResponse } from "next/server"
import { AIService } from "@/lib/ai-service"
import { OptimizedAIService } from "@/lib/ai-service-optimized"
import { validateApiAccess } from "@/lib/api-auth"
import { getCurrentSearchConfig } from "@/lib/search-config"

export const runtime = "edge"

export async function POST(req: NextRequest) {
  // 验证API访问权限
  const authError = validateApiAccess(req)
  if (authError) {
    return authError
  }
  try {
    const searchConfig = getCurrentSearchConfig()
    const { query, tools, useOptimized = searchConfig.deepSearch.useOptimized } = await req.json()

    if (!query || !tools) {
      return NextResponse.json(
        { error: "Query and tools are required" },
        { status: 400 }
      )
    }

    if (!Array.isArray(tools)) {
      return NextResponse.json(
        { error: "Tools must be an array" },
        { status: 400 }
      )
    }

    // 根据参数选择使用优化版或原版AI服务
    let result
    if (useOptimized) {
      console.log('使用优化版深度搜索')
      result = await OptimizedAIService.quickDeepSearch(query, tools)

      // 启动异步分类修正（不等待完成）
      setTimeout(async () => {
        try {
          const corrections = await OptimizedAIService.processAsyncCorrection(result, tools)
          if (corrections.length > 0) {
            console.log(`深度搜索异步修正完成: ${corrections.length} 个工具`)
            // 这里可以通过WebSocket或其他方式通知前端更新
          }
        } catch (error) {
          console.error('深度搜索异步修正失败:', error)
        }
      }, 100)
    } else {
      console.log('使用原版深度搜索')
      result = await AIService.deepSearch(query, tools)
    }

    // 验证返回结果的结构
    if (!result || typeof result !== 'object') {
      throw new Error('AI返回的结果格式无效')
    }

    // 处理优化版和原版的不同返回格式
    let finalResult
    if (useOptimized) {
      // 优化版已经返回了完整的结构
      finalResult = result
    } else {
      // 原版的处理逻辑
      if (Array.isArray(result)) {
        // 如果AI直接返回了推荐工具数组，包装成完整格式
        finalResult = {
          searchSummary: `为您找到 ${result.length} 个相关的社区论坛工具`,
          recommendedTools: result,
          searchInsights: `基于您的查询，推荐了 ${result.length} 个最相关的工具，按相关性排序`
        }
      } else {
        // 如果AI返回了完整的对象格式
        finalResult = result
      }

      // 确保recommendedTools是数组
      if (!finalResult.recommendedTools || !Array.isArray(finalResult.recommendedTools)) {
        finalResult.recommendedTools = []
      }

      // 验证每个推荐工具的结构
      finalResult.recommendedTools = finalResult.recommendedTools.filter(rec =>
        rec && typeof rec === 'object' && rec.id && typeof rec.relevanceScore === 'number'
      )
    }

    return NextResponse.json({
      success: true,
      data: finalResult
    })

  } catch (error) {
    console.error("Deep search API error:", error)

    return NextResponse.json(
      {
        error: "Deep search failed",
        details: error instanceof Error ? error.message : String(error),
      },
      { status: 500 }
    )
  }
}
