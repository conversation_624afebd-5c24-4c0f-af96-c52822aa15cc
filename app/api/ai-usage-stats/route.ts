import { NextRequest, NextResponse } from 'next/server'
import { AIUsageMonitor } from '@/lib/ai-usage-monitor'

/**
 * AI 使用统计 API
 * GET /api/ai-usage-stats
 * 
 * 查询参数：
 * - type: 'today' | 'monthly' | 'trend' (默认: 'today')
 * - days: number (仅当 type='trend' 时有效，默认: 7)
 */
export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url)
    const type = searchParams.get('type') || 'today'
    const days = parseInt(searchParams.get('days') || '7')

    let data = null

    switch (type) {
      case 'today':
        data = await AIUsageMonitor.getTodayUsage()
        break
      
      case 'monthly':
        data = await AIUsageMonitor.getMonthlyUsage()
        break
      
      case 'trend':
        data = await AIUsageMonitor.getRecentUsageTrend(days)
        break
      
      default:
        return NextResponse.json(
          { error: '无效的查询类型，支持: today, monthly, trend' },
          { status: 400 }
        )
    }

    return NextResponse.json({
      success: true,
      type,
      data
    })
  } catch (error) {
    console.error('获取 AI 使用统计失败:', error)
    return NextResponse.json(
      { 
        success: false, 
        error: error instanceof Error ? error.message : '获取统计数据失败' 
      },
      { status: 500 }
    )
  }
}

/**
 * 手动触发统计更新
 * POST /api/ai-usage-stats
 */
export async function POST(request: NextRequest) {
  try {
    const body = await request.json()
    const { action } = body

    if (action === 'refresh') {
      // 触发今日统计更新
      const todayStats = await AIUsageMonitor.getTodayUsage()
      
      return NextResponse.json({
        success: true,
        message: '统计数据已刷新',
        data: todayStats
      })
    }

    return NextResponse.json(
      { error: '无效的操作，支持: refresh' },
      { status: 400 }
    )
  } catch (error) {
    console.error('更新 AI 使用统计失败:', error)
    return NextResponse.json(
      { 
        success: false, 
        error: error instanceof Error ? error.message : '更新统计数据失败' 
      },
      { status: 500 }
    )
  }
}
