import { NextRequest, NextResponse } from 'next/server'
import { validateApiAccess } from '@/lib/api-auth'
import { 
  getCurrentSearchConfig, 
  updateSearchConfig, 
  applyConfigPreset, 
  SEARCH_CONFIG_PRESETS,
  SearchConfig 
} from '@/lib/search-config'

export const runtime = 'edge'

/**
 * 搜索配置管理API
 * GET /api/search-config - 获取当前配置
 * POST /api/search-config - 更新配置
 * PUT /api/search-config/preset - 应用预设配置
 */

// 获取当前搜索配置
export async function GET(req: NextRequest) {
  // 验证API访问权限
  const authError = validateApiAccess(req)
  if (authError) {
    return authError
  }

  try {
    const currentConfig = getCurrentSearchConfig()
    
    return NextResponse.json({
      success: true,
      data: {
        currentConfig,
        availablePresets: Object.keys(SEARCH_CONFIG_PRESETS),
        presets: SEARCH_CONFIG_PRESETS
      }
    })
  } catch (error) {
    console.error('获取搜索配置失败:', error)
    return NextResponse.json(
      { 
        success: false,
        error: 'Failed to get search config',
        details: error instanceof Error ? error.message : String(error)
      },
      { status: 500 }
    )
  }
}

// 更新搜索配置
export async function POST(req: NextRequest) {
  // 验证API访问权限
  const authError = validateApiAccess(req)
  if (authError) {
    return authError
  }

  try {
    const { config } = await req.json()

    if (!config || typeof config !== 'object') {
      return NextResponse.json(
        { 
          success: false,
          error: 'Invalid config object'
        },
        { status: 400 }
      )
    }

    // 更新配置
    updateSearchConfig(config)
    const updatedConfig = getCurrentSearchConfig()

    console.log('搜索配置已更新:', config)

    return NextResponse.json({
      success: true,
      message: '搜索配置更新成功',
      data: {
        updatedConfig,
        changes: config
      }
    })
  } catch (error) {
    console.error('更新搜索配置失败:', error)
    return NextResponse.json(
      { 
        success: false,
        error: 'Failed to update search config',
        details: error instanceof Error ? error.message : String(error)
      },
      { status: 500 }
    )
  }
}

// 应用预设配置
export async function PUT(req: NextRequest) {
  // 验证API访问权限
  const authError = validateApiAccess(req)
  if (authError) {
    return authError
  }

  try {
    const { preset } = await req.json()

    if (!preset || typeof preset !== 'string') {
      return NextResponse.json(
        { 
          success: false,
          error: 'Preset name is required'
        },
        { status: 400 }
      )
    }

    if (!(preset in SEARCH_CONFIG_PRESETS)) {
      return NextResponse.json(
        { 
          success: false,
          error: `Invalid preset: ${preset}`,
          availablePresets: Object.keys(SEARCH_CONFIG_PRESETS)
        },
        { status: 400 }
      )
    }

    // 应用预设配置
    applyConfigPreset(preset as keyof typeof SEARCH_CONFIG_PRESETS)
    const updatedConfig = getCurrentSearchConfig()

    console.log(`已应用搜索配置预设: ${preset}`)

    return NextResponse.json({
      success: true,
      message: `已应用配置预设: ${preset}`,
      data: {
        preset,
        updatedConfig,
        presetConfig: SEARCH_CONFIG_PRESETS[preset as keyof typeof SEARCH_CONFIG_PRESETS]
      }
    })
  } catch (error) {
    console.error('应用搜索配置预设失败:', error)
    return NextResponse.json(
      { 
        success: false,
        error: 'Failed to apply config preset',
        details: error instanceof Error ? error.message : String(error)
      },
      { status: 500 }
    )
  }
}
