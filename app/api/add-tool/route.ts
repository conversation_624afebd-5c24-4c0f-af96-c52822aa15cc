import { NextRequest, NextResponse } from 'next/server'
import { validateApiAccess } from '@/lib/api-auth'
import { ToolsDatabase } from '@/lib/database'
import { ExtendedDatabase } from '@/lib/database-extended'

export const runtime = 'edge'

/**
 * 自动添加工具API端点
 * POST /api/add-tool
 * 
 * 功能：
 * 1. 验证API访问权限
 * 2. 调用AI分析URL
 * 3. 自动保存工具到数据库
 * 4. 记录操作日志
 * 
 * 请求体：
 * {
 *   "url": "https://example.com"
 * }
 * 
 * 请求头：
 * X-API-Key: your-api-access-key
 * 
 * 响应：
 * {
 *   "success": true,
 *   "message": "工具添加成功",
 *   "tool": { ... },
 *   "analysis": { ... }
 * }
 */
export async function POST(request: NextRequest) {
  const startTime = Date.now()
  
  try {
    // 1. 验证API访问权限
    const authError = validateApiAccess(request)
    if (authError) {
      return authError
    }

    // 2. 解析请求体
    let requestBody
    try {
      requestBody = await request.json()
    } catch (error) {
      return NextResponse.json(
        { 
          success: false,
          error: 'Invalid JSON in request body',
          code: 'INVALID_JSON'
        },
        { status: 400 }
      )
    }

    const { url } = requestBody

    // 3. 验证URL参数
    if (!url || typeof url !== 'string') {
      return NextResponse.json(
        { 
          success: false,
          error: 'URL is required and must be a string',
          code: 'INVALID_URL'
        },
        { status: 400 }
      )
    }

    // 验证URL格式
    try {
      new URL(url)
    } catch (error) {
      return NextResponse.json(
        { 
          success: false,
          error: 'Invalid URL format',
          code: 'INVALID_URL_FORMAT'
        },
        { status: 400 }
      )
    }

    // 4. 调用AI分析URL（复用现有的analyze-url逻辑）
    let analysisResult
    try {
      // 获取页面内容
      let pageContent = ""
      try {
        const response = await fetch(url, {
          headers: {
            'User-Agent': 'Mozilla/5.0 (compatible; ToolMaster/1.0; +https://toolmaster.app)'
          }
        })
        if (response.ok) {
          pageContent = await response.text()
        }
      } catch (fetchError) {
        console.warn("Failed to fetch URL content:", fetchError)
        pageContent = `Could not fetch content from ${url}. Please analyze based on the URL itself.`
      }

      // 提取内容片段
      const snippet = pageContent.slice(0, 5000)

      // 构建AI分析提示
      const systemPrompt = `你是一个专业的网站分析和工具分类专家。请根据用户提供的网站URL和页面内容，准确分析并生成以下信息：

1. 工具名称：提取网站的真实名称（如：腾讯视频、GitHub、Figma等）
2. 工具描述：生成50-100字的准确描述，说明工具的主要功能和特点
3. 相关标签：生成3-6个相关的中文标签
4. 分类信息：将工具分类到最合适的三级目录结构中

## 完整分类结构：

**development (开发工具)**
- code-editor (代码编辑): online-editor, ide, text-editor, code-formatter
- version-control (版本控制): git-tools, collaboration, code-review
- api-tools (API工具): api-testing, api-docs, mock-tools
- database (数据库工具): db-client, db-design, db-migration
- deployment (部署运维): ci-cd, monitoring, container

**design (设计工具)**
- ui-design (UI设计): design-software, prototyping, wireframe
- graphic-design (平面设计): image-editor, vector-graphics, photo-editing
- color-tools (颜色工具): color-picker, palette-generator, color-scheme

**productivity (效率工具)**
- note-taking (笔记工具): markdown-editor, note-app, knowledge-base
- task-management (任务管理): todo-list, project-management, time-tracking
- file-management (文件管理): cloud-storage, file-converter, file-sharing

**marketing (营销工具)**
- seo-tools (SEO工具): keyword-research, site-analysis, rank-tracking
- social-media (社交媒体): social-management, content-planning, analytics
- email-marketing (邮件营销): email-builder, automation, analytics

**analytics (数据分析)**
- web-analytics (网站分析): traffic-analysis, user-behavior, conversion-tracking
- business-intelligence (商业智能): dashboard, reporting, data-visualization
- survey-tools (调研工具): survey-builder, feedback-collection, poll-creator

**communication (沟通协作)**
- messaging (即时通讯): chat-app, video-call, team-communication
- email-tools (邮件工具): email-client, email-tracking, signature-generator
- collaboration (协作工具): document-sharing, real-time-editing, whiteboard

**entertainment (娱乐工具)**
- media-streaming (媒体流): video-streaming, music-streaming, podcast
- gaming (游戏): online-games, game-tools, gaming-platform
- social-platform (社交平台): social-network, forum, community

**education (教育学习)**
- online-courses (在线课程): course-platform, skill-learning, certification
- language-learning (语言学习): language-app, translation, pronunciation
- reference (参考资料): dictionary, encyclopedia, academic-resource

**other (其他工具)**
- utility (实用工具): calculator, converter, generator
- lifestyle (生活方式): health-fitness, travel, food-recipe
- finance (金融理财): budgeting, investment, cryptocurrency

请返回JSON格式的结果，包含以下字段：
{
  "name": "工具的真实名称",
  "description": "50-100字的准确描述",
  "tags": ["标签1", "标签2", "标签3"],
  "category": "一级分类ID",
  "subcategory": "二级分类ID",
  "subsubcategory": "三级分类ID"
}`

      const userPrompt = `请分析以下网站：
URL: ${url}
页面内容片段: ${snippet.substring(0, 2000)}`

      // 调用DeepSeek API
      const aiResponse = await fetch(process.env.DEEPSEEK_API_URL || 'https://api.deepseek.com/chat/completions', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${process.env.DEEPSEEK_API_KEY}`
        },
        body: JSON.stringify({
          model: 'deepseek-chat',
          messages: [
            { role: 'system', content: systemPrompt },
            { role: 'user', content: userPrompt }
          ],
          temperature: 0.3,
          max_tokens: 1000,
          stream: false
        })
      })

      if (!aiResponse.ok) {
        throw new Error(`DeepSeek API error: ${aiResponse.status}`)
      }

      const aiData = await aiResponse.json()
      const aiText = aiData.choices?.[0]?.message?.content || ''

      // 解析AI响应
      try {
        // 提取JSON部分
        const jsonMatch = aiText.match(/\{[\s\S]*\}/)
        if (!jsonMatch) {
          throw new Error('No JSON found in AI response')
        }
        
        analysisResult = JSON.parse(jsonMatch[0])
      } catch (parseError) {
        console.error("Failed to parse AI response:", parseError)
        
        // 使用备用分析
        const urlObj = new URL(url)
        const domain = urlObj.hostname.replace('www.', '')
        const name = domain.split('.')[0]
        
        analysisResult = {
          name: name.charAt(0).toUpperCase() + name.slice(1),
          description: `${name}是一个实用的在线工具平台`,
          tags: ['在线工具', '实用工具', 'web应用'],
          category: 'other',
          subcategory: 'utility',
          subsubcategory: 'calculator'
        }
      }

    } catch (error) {
      console.error("AI analysis error:", error)
      return NextResponse.json(
        { 
          success: false,
          error: 'Failed to analyze URL with AI',
          code: 'AI_ANALYSIS_FAILED',
          details: error instanceof Error ? error.message : String(error)
        },
        { status: 500 }
      )
    }

    // 5. 保存工具到数据库
    const toolData = {
      name: analysisResult.name || 'Unknown Tool',
      url: url,
      description: analysisResult.description || '',
      tags: Array.isArray(analysisResult.tags) ? analysisResult.tags : [],
      category: analysisResult.category || 'other',
      subcategory: analysisResult.subcategory || 'utility',
      subsubcategory: analysisResult.subsubcategory || 'calculator',
      sensitive: false
    }

    let savedTool
    try {
      savedTool = await ToolsDatabase.add(toolData)
      if (!savedTool) {
        throw new Error('Failed to save tool to database')
      }
    } catch (error) {
      console.error("Database save error:", error)
      return NextResponse.json(
        { 
          success: false,
          error: 'Failed to save tool to database',
          code: 'DATABASE_SAVE_FAILED',
          details: error instanceof Error ? error.message : String(error)
        },
        { status: 500 }
      )
    }

    // 6. 记录操作日志
    const processingTime = Date.now() - startTime
    try {
      await ExtendedDatabase.createOperationLog(
        'API自动添加工具',
        'add',
        'tool',
        savedTool.id,
        {
          source: 'api',
          url: url,
          toolName: savedTool.name,
          category: `${savedTool.category}/${savedTool.subcategory}/${savedTool.subsubcategory}`,
          tags: savedTool.tags,
          processingTimeMs: processingTime,
          aiAnalysis: analysisResult,
          userAgent: request.headers.get('user-agent') || 'unknown',
          ipAddress: request.ip || request.headers.get('x-forwarded-for') || 'unknown'
        },
        'success'
      )
    } catch (logError) {
      console.error("Failed to create operation log:", logError)
      // 不因为日志失败而影响主要功能
    }

    // 7. 返回成功响应
    return NextResponse.json({
      success: true,
      message: '工具添加成功',
      tool: savedTool,
      analysis: analysisResult,
      processingTime: processingTime
    })

  } catch (error) {
    console.error("Add tool API error:", error)
    
    // 记录错误日志
    try {
      await ExtendedDatabase.createOperationLog(
        'API自动添加工具',
        'add',
        'tool',
        undefined,
        {
          source: 'api',
          error: error instanceof Error ? error.message : String(error),
          processingTimeMs: Date.now() - startTime,
          userAgent: request.headers.get('user-agent') || 'unknown',
          ipAddress: request.ip || request.headers.get('x-forwarded-for') || 'unknown'
        },
        'error',
        error instanceof Error ? error.message : String(error)
      )
    } catch (logError) {
      console.error("Failed to create error log:", logError)
    }

    return NextResponse.json(
      { 
        success: false,
        error: 'Internal server error',
        code: 'INTERNAL_ERROR',
        details: error instanceof Error ? error.message : String(error)
      },
      { status: 500 }
    )
  }
}
