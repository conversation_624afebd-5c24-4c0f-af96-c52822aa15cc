import { NextRequest, NextResponse } from "next/server"
import { validateApiAccess } from "@/lib/api-auth"
import { fetchWebContent } from "@/lib/fetch-content"

export const runtime = "edge"

export async function GET(request: NextRequest) {
  // 验证API访问权限
  const authError = validateApiAccess(request)
  if (authError) {
    return authError
  }
  const { searchParams } = new URL(request.url)
  const url = searchParams.get('url')

  if (!url) {
    return NextResponse.json(
      { error: "URL parameter is required" },
      { status: 400 }
    )
  }

  try {
    const result = await fetchWebContent(url)
    return NextResponse.json(result)
  } catch (error) {
    console.error('Fetch content error:', error)
    return NextResponse.json({
      url,
      title: '',
      snippet: '',
      error: error instanceof Error ? error.message : 'Unknown error',
      success: false
    })
  }
}
