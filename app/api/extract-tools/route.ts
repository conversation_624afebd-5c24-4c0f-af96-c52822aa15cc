import { NextRequest, NextResponse } from 'next/server'
import { AIUsageMonitor } from '@/lib/ai-usage-monitor'
import { fetchWebContent } from '@/lib/fetch-content'
import crypto from 'crypto'

// 简单的内存缓存
const extractionCache = new Map<string, { result: any, timestamp: number }>()
const CACHE_TTL = 24 * 60 * 60 * 1000 // 24小时

// 辅助函数：验证和标准化分类
function validateCategory(category: string): string {
  const validCategories = [
    '开发工具', '设计工具', 'AI工具', '效率工具', '实用服务', '其他工具'
  ]

  if (!category || typeof category !== 'string') {
    return '其他工具'
  }

  const normalizedCategory = category.trim()
  return validCategories.includes(normalizedCategory) ? normalizedCategory : '其他工具'
}

// 辅助函数：生成缓存键
function generateCacheKey(url: string): string {
  return crypto.createHash('md5').update(url.toLowerCase().trim()).digest('hex')
}

// 辅助函数：检查缓存
function getCachedResult(url: string): any | null {
  const cacheKey = generateCacheKey(url)
  const cached = extractionCache.get(cacheKey)

  if (cached && Date.now() - cached.timestamp < CACHE_TTL) {
    return cached.result
  }

  // 清理过期缓存
  if (cached) {
    extractionCache.delete(cacheKey)
  }

  return null
}

// 辅助函数：设置缓存
function setCachedResult(url: string, result: any): void {
  const cacheKey = generateCacheKey(url)
  extractionCache.set(cacheKey, {
    result,
    timestamp: Date.now()
  })

  // 限制缓存大小
  if (extractionCache.size > 100) {
    const oldestKey = extractionCache.keys().next().value
    extractionCache.delete(oldestKey)
  }
}

interface ExtractedTool {
  url: string
  title?: string
  description?: string
  category?: string
  confidence: number
}

interface ExtractionResult {
  sourceUrl: string
  extractedTools: ExtractedTool[]
  totalCount: number
  processingTime: number
  summary?: {
    totalFound: number
    highConfidence: number
    categories: string[]
  }
}

/**
 * 网站工具提取 API
 * POST /api/extract-tools
 */
export async function POST(request: NextRequest) {
  const startTime = Date.now()
  let success = false
  let errorMessage: string | undefined
  let inputTokens = 0
  let outputTokens = 0

  try {
    const { url, options = {} } = await request.json()

    if (!url || typeof url !== 'string') {
      return NextResponse.json(
        { success: false, error: '请提供有效的URL' },
        { status: 400 }
      )
    }

    // 验证URL格式
    try {
      new URL(url)
    } catch {
      return NextResponse.json(
        { success: false, error: 'URL格式无效' },
        { status: 400 }
      )
    }

    // 检查是否为被禁止的域名
    const blockedDomains = [
      'facebook.com', 'twitter.com', 'instagram.com', 'linkedin.com',
      'youtube.com', 'tiktok.com', 'weibo.com', 'qq.com'
    ]

    const urlObj = new URL(url)
    const domain = urlObj.hostname.toLowerCase()

    if (blockedDomains.some(blocked => domain.includes(blocked))) {
      return NextResponse.json(
        { success: false, error: '不支持分析社交媒体网站' },
        { status: 400 }
      )
    }

    console.log(`开始分析网站: ${url}`)

    // 0. 检查缓存
    const cachedResult = getCachedResult(url)
    if (cachedResult) {
      console.log('使用缓存结果')
      success = true
      return NextResponse.json({
        success: true,
        data: {
          ...cachedResult,
          processingTime: Date.now() - startTime,
          cached: true
        }
      })
    }

    // 1. 获取网页内容
    const responseData = await fetchWebContent(url)

    if (!responseData.success) {
      const error = responseData.error || '无法获取网页内容'
      if (error.includes('timeout') || error.includes('aborted')) {
        throw new Error('网站响应超时，请稍后重试或尝试其他网站')
      } else if (error.includes('403') || error.includes('Forbidden')) {
        throw new Error('网站拒绝访问，可能有反爬虫保护')
      } else if (error.includes('404') || error.includes('Not Found')) {
        throw new Error('网页不存在，请检查URL是否正确')
      } else {
        throw new Error(`获取网页失败: ${error}`)
      }
    }

    const content = responseData.snippet || responseData.title || ''

    if (!content || typeof content !== 'string') {
      throw new Error('无法获取网页内容')
    }

    if (content.length < 50) {
      throw new Error('网页内容过短，可能是动态加载网站或访问受限')
    }

    console.log(`获取到网页内容，长度: ${content.length} 字符`)

    // 2. 构建AI提示词
    const systemPrompt = `你是一个专业的网页工具提取专家，专门识别和提取网站中的实用工具链接。

## 核心任务
从HTML内容中精确提取所有实用工具的链接，重点关注那些能为用户提供具体功能的在线服务。

## 工具识别标准

### ✅ 优先提取（高置信度 0.8-1.0）
- **在线工具**: 图片编辑、文档转换、代码编辑器、计算器等
- **开发工具**: API服务、代码库、框架、IDE、测试工具等
- **设计工具**: UI设计、图标库、字体库、配色工具等
- **效率工具**: 项目管理、协作平台、自动化工具等
- **AI工具**: 聊天机器人、图像生成、文本处理等
- **实用服务**: 文件存储、邮件服务、数据分析等

### ⚠️ 谨慎判断（中置信度 0.5-0.8）
- 可能是工具但描述不够明确的链接
- 需要注册才能使用的服务
- 功能复杂的综合性平台

### ❌ 明确排除（低置信度 0.0-0.5）
- 纯信息类网站（新闻、博客、文档）
- 社交媒体平台（微博、Twitter、Facebook）
- 电商购物网站
- 导航菜单（首页、关于我们、联系我们）
- 媒体文件（图片、视频、PDF下载）
- 广告和推广链接

## 分析策略
1. **上下文分析**: 重点关注链接周围的描述文字
2. **标题识别**: 提取链接的标题和alt文本
3. **URL模式**: 识别典型的工具网站URL模式
4. **功能描述**: 寻找明确的功能说明文字

## 输出要求
严格按照以下JSON格式输出，不要添加任何其他文字：

{
  "extractedTools": [
    {
      "url": "完整的URL地址（必须以http://或https://开头）",
      "title": "工具名称（从链接文本或标题提取）",
      "description": "工具功能描述（从周围文本提取，限制在100字内）",
      "category": "工具分类（从以下选择：开发工具、设计工具、AI工具、效率工具、实用服务、其他工具）",
      "confidence": 0.95
    }
  ],
  "summary": {
    "totalFound": 数量,
    "highConfidence": "置信度>0.8的数量",
    "categories": ["发现的主要分类列表"]
  }
}`

    const userPrompt = `请分析以下HTML内容并提取工具链接：

${content.substring(0, 8000)}` // 限制内容长度避免token过多

    // 3. 调用AI分析
    console.log('开始AI分析...')

    const aiResponse = await fetch('https://api.deepseek.com/chat/completions', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${process.env.DEEPSEEK_API_KEY}`
      },
      body: JSON.stringify({
        model: 'deepseek-chat',
        messages: [
          { role: 'system', content: systemPrompt },
          { role: 'user', content: userPrompt }
        ],
        temperature: 0.3,
        max_tokens: 2000,
        stream: false
      })
    })

    if (!aiResponse.ok) {
      throw new Error(`AI分析失败: ${aiResponse.status} ${aiResponse.statusText}`)
    }

    const aiResult = await aiResponse.json()
    const aiContent = aiResult.choices[0]?.message?.content || ''

    // 获取token使用情况
    if (aiResult.usage) {
      inputTokens = aiResult.usage.prompt_tokens || 0
      outputTokens = aiResult.usage.completion_tokens || 0
    } else {
      // 估算token使用量
      inputTokens = Math.ceil((systemPrompt.length + userPrompt.length) / 4)
      outputTokens = Math.ceil(aiContent.length / 4)
    }

    console.log('AI分析完成，开始解析结果...')

    // 4. 解析AI返回的JSON
    let parsedResult
    try {
      // 尝试提取JSON部分
      const jsonMatch = aiContent.match(/\{[\s\S]*\}/)
      if (jsonMatch) {
        parsedResult = JSON.parse(jsonMatch[0])
      } else {
        throw new Error('AI返回内容中未找到有效的JSON')
      }
    } catch (parseError) {
      console.error('JSON解析失败:', parseError)
      throw new Error('AI返回的数据格式无效')
    }

    // 5. 验证和处理结果
    const extractedTools: ExtractedTool[] = []

    if (parsedResult.extractedTools && Array.isArray(parsedResult.extractedTools)) {
      for (const tool of parsedResult.extractedTools) {
        if (tool.url && typeof tool.url === 'string') {
          try {
            // 验证URL格式
            let toolUrl = tool.url.trim()

            // 确保URL有协议
            if (!toolUrl.startsWith('http://') && !toolUrl.startsWith('https://')) {
              toolUrl = `https://${toolUrl}`
            }

            // 验证URL有效性
            const urlObj = new URL(toolUrl)

            // 过滤明显的非工具域名
            const domain = urlObj.hostname.toLowerCase()
            const blockedDomains = [
              'facebook.com', 'twitter.com', 'instagram.com', 'linkedin.com',
              'youtube.com', 'tiktok.com', 'weibo.com', 'qq.com', 'wechat.com',
              'taobao.com', 'tmall.com', 'jd.com', 'amazon.com', 'ebay.com'
            ]

            if (blockedDomains.some(blocked => domain.includes(blocked))) {
              console.warn('跳过被屏蔽的域名:', domain)
              continue
            }

            // 验证和清理工具信息
            const title = (tool.title || '').trim().substring(0, 100)
            const description = (tool.description || '').trim().substring(0, 200)
            const category = validateCategory(tool.category) || '其他工具'
            const confidence = Math.min(Math.max(tool.confidence || 0.5, 0), 1)

            // 只保留置信度较高的工具
            if (confidence >= 0.3) {
              extractedTools.push({
                url: toolUrl,
                title: title || urlObj.hostname,
                description: description,
                category: category,
                confidence: confidence
              })
            }
          } catch (error) {
            console.warn('跳过无效URL:', tool.url, error)
          }
        }
      }
    }

    // 6. 去重处理
    const uniqueTools = extractedTools.filter((tool, index, self) =>
      index === self.findIndex(t => t.url === tool.url)
    )

    // 7. 按置信度排序
    uniqueTools.sort((a, b) => b.confidence - a.confidence)

    // 8. 限制数量
    const maxTools = options.maxTools || 100
    const finalTools = uniqueTools.slice(0, maxTools)

    const processingTime = Date.now() - startTime

    const result: ExtractionResult = {
      sourceUrl: url,
      extractedTools: finalTools,
      totalCount: finalTools.length,
      processingTime,
      summary: {
        totalFound: finalTools.length,
        highConfidence: finalTools.filter(t => t.confidence > 0.8).length,
        categories: [...new Set(finalTools.map(t => t.category).filter(Boolean))]
      }
    }

    success = true
    console.log(`分析完成，提取到 ${result.totalCount} 个工具`)

    // 缓存结果（不包含处理时间）
    const cacheableResult = {
      sourceUrl: result.sourceUrl,
      extractedTools: result.extractedTools,
      totalCount: result.totalCount,
      summary: result.summary
    }
    setCachedResult(url, cacheableResult)

    return NextResponse.json({
      success: true,
      data: result
    })

  } catch (error) {
    success = false
    errorMessage = error instanceof Error ? error.message : '分析失败'
    console.error('工具提取失败:', error)

    return NextResponse.json(
      {
        success: false,
        error: errorMessage
      },
      { status: 500 }
    )
  } finally {
    // 记录AI使用情况
    const responseTime = Date.now() - startTime
    AIUsageMonitor.logUsage({
      apiType: 'extract-tools',
      model: 'deepseek-chat',
      inputTokens,
      outputTokens,
      responseTimeMs: responseTime,
      success,
      errorMessage,
      requestId: `extract-${Date.now()}`
    }).catch(err => {
      console.error('记录AI使用情况失败:', err)
    })
  }
}
