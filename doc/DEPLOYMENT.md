# ToolMaster - 部署指南

本文档提供了将 ToolMaster 部署到 Vercel 平台的指南。

## 准备工作

1.  **Vercel 账户**: 确保你有一个 Vercel 账户。如果没有，可以在 [vercel.com](https://vercel.com) 注册。
2.  **Vercel CLI**: 安装 Vercel 命令行工具。
    \`\`\`bash
    npm install -g vercel
    \`\`\`
3.  **登录 Vercel**: 在你的终端中登录 Vercel。
    \`\`\`bash
    vercel login
    \`\`\`
    这会打开一个浏览器窗口，让你通过 Vercel 账户进行认证。

## 部署到 Vercel (推荐)

Vercel 是 Next.js 的创建者，提供了最佳的部署体验。

1.  **克隆仓库 (如果尚未克隆)**

    如果你还没有克隆 ToolMaster 仓库，请先克隆它：

    \`\`\`bash
    git clone https://github.com/your-username/toolmaster.git
    cd toolmaster
    \`\`\`

2.  **安装依赖**

    在项目根目录安装所有必要的依赖：

    \`\`\`bash
    npm install
    # 或者
    yarn install
    # 或者
    pnpm install
    \`\`\`

3.  **配置环境变量**

    ToolMaster 应用程序使用 `OPENAI_API_KEY` 来集成 AI 功能。你需要将此环境变量添加到你的 Vercel 项目中。

    #### 方法一：通过 Vercel CLI 添加

    在项目根目录运行以下命令，并按照提示操作：

    \`\`\`bash
    vercel env add OPENAI_API_KEY
    \`\`\`

    当提示输入值时，粘贴你的 OpenAI API 密钥。选择 `Production`, `Preview`, `Development` 环境。

    #### 方法二：通过 Vercel Dashboard 添加

    1.  访问你的 Vercel Dashboard ([vercel.com/dashboard](https://vercel.com/dashboard))。
    2.  选择你的 ToolMaster 项目。
    3.  导航到 **Settings** (设置) > **Environment Variables** (环境变量)。
    4.  点击 **Add New** (添加新变量)。
    5.  在 **Name** 字段输入 `OPENAI_API_KEY`。
    6.  在 **Value** 字段粘贴你的 OpenAI API 密钥。
    7.  选择所有环境 (`Production`, `Preview`, `Development`)。
    8.  点击 **Add** (添加)。

4.  **部署应用程序**

    在项目根目录运行以下命令来部署你的应用程序：

    \`\`\`bash
    vercel deploy
    \`\`\`

    Vercel CLI 会引导你完成部署过程，包括选择项目名称、关联 Git 仓库等。如果这是你第一次部署，它可能会提示你创建一个新项目。

    部署完成后，Vercel 会提供一个部署 URL。

5.  **重新部署 (如果需要)**

    如果你对代码进行了更改并希望重新部署，只需再次运行 `vercel deploy` 命令。Vercel 会检测到更改并创建一个新的部署。

Vercel 将会自动构建并部署你的应用，并提供一个可访问的 URL。每次你向连接的 Git 分支推送代码时，Vercel 都会自动重新部署。

## 本地开发

为了本地开发，请确保您已安装 Node.js。

1.  克隆仓库。
2.  安装依赖: `npm install` 或 `yarn install`。
3.  运行开发服务器: `npm run dev` 或 `yarn dev`。

## 环境变量

如果你的应用需要访问外部服务（如 AI API），请确保在部署环境中设置相应的环境变量。例如：

-   `OPENAI_API_KEY`: 用于访问 OpenAI API 的密钥。
-   `NEXT_PUBLIC_ANALYTICS_ID`: (如果使用) 公开的分析 ID。

在 Vercel 中，这些可以在项目设置中配置。在 Docker 或自托管环境中，你可以在 `Dockerfile` 中使用 `ENV` 指令或在启动命令前设置。

## 部署到其他平台

### Docker

你可以将 ToolMaster 打包成 Docker 镜像并部署到任何支持 Docker 的环境。

1.  **创建 Dockerfile**：在项目根目录创建 `Dockerfile`。

    \`\`\`dockerfile
    # 使用官方 Node.js 镜像作为基础镜像
    FROM node:20-alpine AS base

    # 设置工作目录
    WORKDIR /app

    # 复制 package.json 和 package-lock.json (或 yarn.lock / pnpm-lock.yaml)
    COPY package*.json ./

    # 安装依赖
    RUN npm install --frozen-lockfile

    # 复制所有文件到工作目录
    COPY . .

    # 构建 Next.js 应用
    RUN npm run build

    # 生产环境镜像
    FROM node:20-alpine AS runner

    WORKDIR /app

    # 复制构建好的应用和 node_modules
    COPY --from=base /app/.next ./.next
    COPY --from=base /app/node_modules ./node_modules
    COPY --from=base /app/public ./public
    COPY --from=base /app/package.json ./package.json

    # 设置环境变量 (如果需要)
    # ENV OPENAI_API_KEY=your_api_key_here

    # 暴露端口
    EXPOSE 3000

    # 启动应用
    CMD ["npm", "start"]
    \`\`\`

2.  **构建 Docker 镜像**：

    \`\`\`bash
    docker build -t toolmaster-app .
    \`\`\`

3.  **运行 Docker 容器**：

    \`\`\`bash
    docker run -p 3000:3000 toolmaster-app
    \`\`\`

    应用将在 `http://localhost:3000` 运行。

### 自托管 (Node.js 服务器)

你也可以在自己的 Node.js 服务器上托管 ToolMaster。

1.  **构建应用**：

    \`\`\`bash
    npm run build
    \`\`\`

2.  **安装生产依赖**：

    \`\`\`bash
    npm install --production
    \`\`\`

3.  **启动应用**：

    \`\`\`bash
    npm run start
    \`\`\`

    应用将在默认端口 3000 运行。你可以使用 `PORT` 环境变量来指定端口：

    \`\`\`bash
    PORT=8080 npm run start
    \`\`\`

    **注意**：你需要一个进程管理器（如 PM2 或 systemd）来确保应用在服务器重启后自动启动，并保持持续运行。

## 注意事项

-   **环境变量**：确保在部署环境中设置所有必要的环境变量。
-   **数据持久化**：ToolMaster 默认使用浏览器本地存储。如果您需要跨设备或持久化存储数据，您需要集成一个后端数据库（例如 Supabase, Neon 等）。
-   **HTTPS**：建议为您的部署启用 HTTPS，以确保数据传输安全。Vercel 会自动处理 HTTPS。

## 常见问题

-   **部署失败**: 检查终端输出的错误信息。通常是由于缺少依赖、配置错误或环境变量问题。
-   **AI 功能不工作**: 确保 `OPENAI_API_KEY` 环境变量已正确配置，并且你的 API 密钥有效。
-   **本地存储数据**: 请注意，Vercel 部署是无状态的。本地存储的数据仅在你的浏览器中有效，不会在部署之间同步或持久化。如果你需要持久化数据，考虑集成一个数据库（例如 Supabase, Neon 等）。

如果你遇到任何问题，可以访问 Vercel 的官方文档或寻求社区帮助。
