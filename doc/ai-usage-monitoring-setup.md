# AI API 费用监控系统设置指南

## 概述

本文档介绍如何设置和使用 AI API 费用监控系统，该系统用于监控 DeepSeek API 的使用情况和费用。

## 数据库设置

### 1. 执行 SQL 脚本

在 Supabase 数据库中执行以下 SQL 脚本：

```bash
# 在 Supabase SQL Editor 中执行
./sql/ai-usage-monitoring.sql
```

### 2. 数据库表结构

系统会创建以下三个主要表：

#### `ai_usage_logs` - AI 使用详细日志表
- 记录每次 API 调用的详细信息
- 包含 token 使用量、费用、响应时间等
- 支持按 API 类型、时间等维度查询

#### `ai_usage_daily_summary` - 每日汇总统计表
- 每日的使用统计汇总
- 包含调用次数、成功率、总费用等
- 支持按 API 类型分组的统计信息

#### `ai_usage_monthly_summary` - 每月汇总统计表
- 每月的使用统计汇总
- 包含月度趋势和费用分布
- 支持历史数据分析

### 3. 索引优化

系统自动创建以下索引以提高查询性能：
- `idx_ai_usage_logs_api_type` - API 类型索引
- `idx_ai_usage_logs_created_at` - 创建时间索引
- `idx_ai_usage_logs_success` - 成功状态索引
- `idx_ai_usage_logs_date_created` - 日期索引

## 功能特性

### 1. 自动监控
- 自动拦截所有 DeepSeek API 调用
- 实时记录 token 使用量和费用
- 支持缓存命中和优惠时段的费用计算

### 2. 费用计算
基于 DeepSeek 官方价格表进行精确计算：

#### deepseek-chat 模型价格（每百万 tokens）
- **标准时段（08:30-00:30）**：
  - 输入（缓存命中）：0.5元
  - 输入（缓存未命中）：2元
  - 输出：8元
- **优惠时段（00:30-08:30）**：
  - 输入（缓存命中）：0.25元
  - 输入（缓存未命中）：1元
  - 输出：4元

### 3. 监控指标
- **费用统计**：今日/本月费用、优惠时段费用
- **使用量统计**：调用次数、token 使用量、成功率
- **性能指标**：平均响应时间、最大响应时间
- **功能分布**：各 API 类型的使用情况

### 4. API 类型支持
- `deep-search` - 深度搜索
- `global-search` - 全网搜索
- `analyze-url` - URL 分析
- `add-tool` - 添加工具

## 使用方法

### 1. 前端界面
在 ToolMaster 应用中：
1. 点击"数据管理"按钮
2. 选择"AI费用"标签页
3. 查看实时的使用统计和费用信息
4. 点击"刷新数据"获取最新统计

### 2. API 接口
```typescript
// 获取今日使用统计
GET /api/ai-usage-stats?type=today

// 获取本月使用统计
GET /api/ai-usage-stats?type=monthly

// 获取使用趋势（最近7天）
GET /api/ai-usage-stats?type=trend&days=7

// 手动刷新统计
POST /api/ai-usage-stats
{
  "action": "refresh"
}
```

### 3. 数据查询示例

#### 查询今日使用情况
```sql
SELECT * FROM ai_usage_daily_summary 
WHERE date_only = CURRENT_DATE;
```

#### 查询最近7天的费用趋势
```sql
SELECT date_only, total_cost_yuan, total_calls 
FROM ai_usage_daily_summary 
WHERE date_only >= CURRENT_DATE - INTERVAL '7 days'
ORDER BY date_only DESC;
```

#### 查询各 API 类型的使用分布
```sql
SELECT api_type, 
       COUNT(*) as calls,
       SUM(total_tokens) as total_tokens,
       SUM(cost_yuan) as total_cost,
       AVG(response_time_ms) as avg_response_time
FROM ai_usage_logs 
WHERE DATE(created_at) = CURRENT_DATE
GROUP BY api_type;
```

## 测试数据

如需插入测试数据进行验证，可以取消注释 SQL 文件末尾的示例数据插入语句。

## 注意事项

1. **性能影响**：监控系统采用异步记录方式，不会影响主业务流程性能
2. **数据隐私**：用户查询内容会被截断到200字符以保护隐私
3. **费用精度**：费用计算精确到0.0001元（4位小数）
4. **时区处理**：优惠时段判断基于北京时间（UTC+8）

## 故障排除

### 常见问题

1. **数据不显示**：检查数据库表是否正确创建
2. **费用计算错误**：确认 DeepSeek API 返回的 usage 信息
3. **性能问题**：检查索引是否正确创建

### 日志查看
```javascript
// 浏览器控制台查看监控日志
console.log('AI 使用日志已记录: deep-search, tokens: 230, 费用: ¥0.0024')
```

## 更新历史

- **2025-08-02**：初始版本发布
  - 支持 DeepSeek API 监控
  - 实现费用计算和统计功能
  - 添加前端展示界面
