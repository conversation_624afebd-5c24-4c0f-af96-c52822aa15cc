# 🗄️ ToolMaster 存储监控功能指南

## 📋 功能概述

ToolMaster 现已集成 Supabase 数据库存储监控功能，帮助您实时了解数据库容量使用情况，合理规划存储需求。

### 🎯 **核心功能**

1. **实时存储监控** - 监控数据库、WAL、系统文件的存储使用情况
2. **表级别详情** - 查看各个数据表的存储占用和行数统计
3. **容量预估** - 基于当前数据预估 1万、5万、10万工具时的存储需求
4. **智能缓存** - 5分钟缓存机制，平衡实时性和性能
5. **容量告警** - 当存储使用率超过 80% 时显示告警提示

## 🚀 **快速开始**

### 1. 部署 SQL 函数

首先需要在 Supabase 中部署存储监控所需的 SQL 函数：

1. 登录 [Supabase Dashboard](https://supabase.com/dashboard)
2. 选择您的项目
3. 进入 **SQL Editor**
4. 执行 `sql/storage-monitoring-functions.sql` 中的所有 SQL 代码

### 2. 访问存储监控

1. 打开 ToolMaster 应用
2. 点击右上角的 **数据管理** 按钮
3. 切换到 **存储监控** 标签页
4. 等待数据加载完成

## 📊 **功能详解**

### **存储概览卡片**

显示四个关键指标：

- **数据库大小** - 纯数据库内容大小
- **总磁盘使用** - 包含数据库 + WAL + 系统文件
- **WAL 日志** - 写入日志文件大小
- **使用率** - 相对于免费额度 (500MB) 的使用百分比

### **容量使用进度条**

- 直观显示当前存储使用情况
- 当使用率超过 80% 时显示橙色告警
- 显示已用空间和剩余空间

### **表存储详情**

- 列出所有数据表的存储占用
- 显示每个表的行数和存储百分比
- 按存储大小降序排列

### **容量预估**

基于当前数据密度预估未来存储需求：

- **1万工具** - 预估存储需求和是否超出限制
- **5万工具** - 中期容量规划参考
- **10万工具** - 长期容量规划参考

每个预估都会标注是否会超出免费额度 (500MB) 或 Pro 额度 (8GB)。

## ⚙️ **技术实现**

### **数据获取方式**

1. **优先使用 RPC 函数** - 通过 Supabase RPC 调用 PostgreSQL 系统函数
2. **降级到估算模式** - 当 RPC 不可用时，基于行数和平均大小估算
3. **智能缓存机制** - 结果缓存 5 分钟，避免频繁查询影响性能

### **核心 SQL 查询**

```sql
-- 数据库总大小
SELECT pg_size_pretty(sum(pg_database_size(pg_database.datname))) 
FROM pg_database;

-- WAL 文件大小
SELECT pg_size_pretty(sum(size)) FROM pg_ls_waldir();

-- 表级别大小
SELECT 
  tablename,
  pg_size_pretty(pg_total_relation_size(schemaname||'.'||tablename)) as size,
  pg_total_relation_size(schemaname||'.'||tablename) as bytes
FROM pg_tables 
WHERE schemaname = 'public'
ORDER BY pg_total_relation_size(schemaname||'.'||tablename) DESC;
```

### **容量预估算法**

```typescript
// 计算平均每个工具的存储占用
const averageSizePerTool = currentToolCount > 0 
  ? totalTableBytes / currentToolCount 
  : 1024; // 默认 1KB

// 预估指定数量工具的存储需求
const estimatedSize = toolCount * averageSizePerTool;
```

## 📈 **使用建议**

### **容量规划**

- **免费计划 (500MB)**：适合 < 5000 个工具
- **Pro 计划 (8GB)**：适合 < 80000 个工具
- **Enterprise 计划**：无限制，适合大规模部署

### **性能优化**

- 存储监控数据每 5 分钟自动刷新
- 可手动点击 "刷新数据" 按钮立即更新
- 避免频繁刷新，以免影响数据库性能

### **告警处理**

当存储使用率超过 80% 时：

1. **立即行动**：
   - 检查是否有重复或无效数据
   - 清理不必要的操作日志
   - 考虑升级到 Pro 计划

2. **长期规划**：
   - 评估数据增长趋势
   - 制定数据归档策略
   - 考虑数据库优化方案

## 🔧 **故障排除**

### **常见问题**

**Q: 显示 "未知" 或数据加载失败**
A: 可能是 SQL 函数未正确部署，请检查 Supabase SQL Editor 中是否成功执行了所有函数。

**Q: 数据更新不及时**
A: 存储监控使用 5 分钟缓存，可点击 "刷新数据" 按钮强制更新。

**Q: 容量预估不准确**
A: 预估基于当前数据密度，实际使用可能因数据类型和索引而有所差异。

### **调试步骤**

1. 检查浏览器控制台是否有错误信息
2. 确认 Supabase 连接正常
3. 验证 SQL 函数是否正确部署
4. 检查数据库权限设置

## 📝 **更新日志**

### v1.0.0 (2025-01-08)
- ✅ 初始版本发布
- ✅ 支持数据库大小监控
- ✅ 支持表级别存储详情
- ✅ 支持容量预估功能
- ✅ 支持智能缓存机制
- ✅ 支持容量告警提示

## 🤝 **技术支持**

如果您在使用过程中遇到问题，请：

1. 查看浏览器控制台错误信息
2. 检查 Supabase 项目状态
3. 确认 SQL 函数部署情况
4. 联系技术支持获取帮助

---

**注意**：存储监控功能需要 Supabase 数据库支持，请确保您的项目已正确配置 Supabase 连接。
