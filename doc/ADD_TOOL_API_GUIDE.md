# 🔧 ToolMaster - 添加工具API使用指南

## 📋 概述

ToolMaster 现在提供了一个便捷的API端点，允许您通过简单的HTTP请求自动添加工具，无需打开网页界面。API会自动调用AI分析URL，识别工具信息，并保存到数据库中。

## 🚀 API 端点信息

- **URL**: `POST /api/add-tool`
- **认证**: 需要API访问密钥
- **功能**: 自动分析URL并添加工具到数据库
- **日志**: 所有操作都会记录在操作日志中

## 🔑 认证方式

API使用基于密钥的认证方式。您需要在请求头中包含有效的API访问密钥：

```
X-API-Key: YOUR_API_ACCESS_KEY
```

> **获取API密钥**: 首次访问ToolMaster网页时，输入正确的访问密码后，系统会自动生成并存储API密钥。

## 📝 请求格式

### 请求头
```
Content-Type: application/json
X-API-Key: YOUR_API_ACCESS_KEY
```

### 请求体
```json
{
  "url": "https://example.com"
}
```

### 参数说明
- `url` (必填): 要添加的工具的URL地址，必须是有效的HTTP/HTTPS URL

## 📤 响应格式

### 成功响应 (200)
```json
{
  "success": true,
  "message": "工具添加成功",
  "tool": {
    "id": "uuid-string",
    "name": "工具名称",
    "url": "https://example.com",
    "description": "工具描述",
    "tags": ["标签1", "标签2"],
    "category": "development",
    "subcategory": "code-editor",
    "subsubcategory": "online-editor",
    "addedAt": "2024-01-01T00:00:00.000Z",
    "sensitive": false
  },
  "analysis": {
    "name": "AI分析的工具名称",
    "description": "AI生成的描述",
    "tags": ["AI生成的标签"],
    "category": "AI分类结果",
    "subcategory": "AI子分类",
    "subsubcategory": "AI子子分类"
  },
  "processingTime": 1234
}
```

### 错误响应
```json
{
  "success": false,
  "error": "错误描述",
  "code": "ERROR_CODE",
  "details": "详细错误信息"
}
```

## 💻 cURL 命令示例

### 基本用法
```bash
curl -X POST "https://your-domain.com/api/add-tool" \
  -H "Content-Type: application/json" \
  -H "X-API-Key: YOUR_API_ACCESS_KEY" \
  -d '{"url": "https://github.com"}'
```

### 本地开发测试
```bash
curl -X POST "http://localhost:3000/api/add-tool" \
  -H "Content-Type: application/json" \
  -H "X-API-Key: YOUR_API_ACCESS_KEY" \
  -d '{"url": "https://github.com"}'
```

### 添加多个工具的脚本示例
```bash
#!/bin/bash

API_KEY="YOUR_API_ACCESS_KEY"
BASE_URL="https://your-domain.com"

# 工具URL列表
urls=(
  "https://github.com"
  "https://figma.com"
  "https://notion.so"
  "https://vercel.com"
)

for url in "${urls[@]}"; do
  echo "添加工具: $url"
  curl -X POST "$BASE_URL/api/add-tool" \
    -H "Content-Type: application/json" \
    -H "X-API-Key: $API_KEY" \
    -d "{\"url\": \"$url\"}" \
    -w "\nHTTP状态: %{http_code}\n\n"
  sleep 2  # 避免请求过于频繁
done
```

## 🔧 Postman 使用方法

1. **创建新请求**
   - 方法: `POST`
   - URL: `https://your-domain.com/api/add-tool`

2. **设置请求头**
   ```
   Content-Type: application/json
   X-API-Key: YOUR_API_ACCESS_KEY
   ```

3. **设置请求体** (选择 raw + JSON)
   ```json
   {
     "url": "https://github.com"
   }
   ```

4. **发送请求**

## ⚠️ 错误代码说明

| 错误代码 | HTTP状态 | 说明 |
|---------|---------|------|
| `API_KEY_NOT_CONFIGURED` | 500 | 服务器未配置API密钥 |
| `API_KEY_MISSING` | 401 | 请求中缺少API密钥 |
| `API_KEY_INVALID` | 403 | API密钥无效 |
| `INVALID_JSON` | 400 | 请求体JSON格式错误 |
| `INVALID_URL` | 400 | URL参数缺失或格式错误 |
| `INVALID_URL_FORMAT` | 400 | URL格式不正确 |
| `AI_ANALYSIS_FAILED` | 500 | AI分析失败 |
| `DATABASE_SAVE_FAILED` | 500 | 数据库保存失败 |
| `INTERNAL_ERROR` | 500 | 服务器内部错误 |

## 🎯 使用场景

### 1. 批量导入工具
当您发现多个有用的工具时，可以快速批量添加：
```bash
# 创建工具列表文件 tools.txt
echo "https://github.com" >> tools.txt
echo "https://figma.com" >> tools.txt

# 批量添加
while read url; do
  curl -X POST "https://your-domain.com/api/add-tool" \
    -H "Content-Type: application/json" \
    -H "X-API-Key: YOUR_API_KEY" \
    -d "{\"url\": \"$url\"}"
done < tools.txt
```

### 2. 浏览器书签同步
结合浏览器扩展或脚本，自动同步书签到ToolMaster。

### 3. 自动化工作流
集成到CI/CD流程或定时任务中，自动发现和添加新工具。

## 🔍 功能特性

### AI智能分析
- 自动提取网站标题和描述
- 智能生成相关标签
- 自动分类到合适的三级目录结构
- 支持多种网站类型识别

### 数据完整性
- 自动验证URL格式
- 防止重复添加相同工具
- 完整的错误处理和回滚机制

### 操作审计
- 所有API调用都会记录操作日志
- 包含请求来源、处理时间等详细信息
- 支持在网页端查看操作历史

## 🧪 测试验证

### 在线测试页面
访问 `/test/test-add-tool-api.html` 进行交互式测试。

### 测试用例
```bash
# 测试成功案例
curl -X POST "http://localhost:3000/api/add-tool" \
  -H "Content-Type: application/json" \
  -H "X-API-Key: YOUR_API_KEY" \
  -d '{"url": "https://github.com"}'

# 测试错误处理
curl -X POST "http://localhost:3000/api/add-tool" \
  -H "Content-Type: application/json" \
  -H "X-API-Key: wrong-key" \
  -d '{"url": "https://github.com"}'
```

## 📊 性能说明

- **平均响应时间**: 2-5秒（包含AI分析时间）
- **并发限制**: 建议每秒不超过1个请求
- **超时设置**: 30秒请求超时
- **缓存机制**: AI分析结果会缓存，相同URL的重复请求会更快

## 🔒 安全注意事项

1. **API密钥保护**: 不要在客户端代码中暴露API密钥
2. **HTTPS使用**: 生产环境请使用HTTPS协议
3. **访问控制**: API密钥具有完整的数据访问权限
4. **日志监控**: 定期检查操作日志，发现异常访问

## 📞 技术支持

如果您在使用过程中遇到问题：

1. 检查API密钥是否正确
2. 验证URL格式是否有效
3. 查看操作日志中的错误信息
4. 使用测试页面进行调试

---

**更新时间**: 2024-01-01  
**版本**: v1.0.0
