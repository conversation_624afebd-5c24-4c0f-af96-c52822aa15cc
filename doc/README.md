# 📚 ToolMaster 文档目录

本目录包含 ToolMaster 项目的所有文档文件。

## 📋 文档分类

### 🚀 部署相关
- `DEPLOYMENT.md` - 部署指南
- `VERCEL_DEPLOYMENT.md` - Vercel 部署文档

### 🔧 功能开发
- `ADD_TOOL_API_GUIDE.md` - 添加工具API使用指南
- `ADD_TOOL_API_TEST_REPORT.md` - API测试验证报告
- `CATEGORY_UPDATE_GUIDE.md` - 分类更新指南
- `DATA_MANAGEMENT_TEST_GUIDE.md` - 数据管理测试指南

### 🔍 验证报告
- `BACKUP_RESTORE_VERIFICATION_REPORT.md` - 备份恢复验证报告
- `CATEGORY_STRUCTURE_UPDATE_REPORT.md` - 分类结构更新报告
- `DATABASE_PERSISTENCE_VERIFICATION.md` - 数据库持久化验证
- `EXPORT_LOGGING_FIX_REPORT.md` - 导出日志修复报告
- `FINAL_VERIFICATION_REPORT.md` - 最终验证报告
- `NAMING_CONSISTENCY_REPORT.md` - 命名一致性报告
- `SECURITY_VERIFICATION.md` - 安全验证报告

### 🛠️ 技术实现
- `DATA_STORAGE.md` - 数据存储说明
- `MERGE_TOOL_FIX_SUMMARY.md` - 工具合并修复总结
- `TENCENT_COS_BACKUP_IMPLEMENTATION_REPORT.md` - 腾讯云COS备份实现报告
- `TEST_TOAST.md` - Toast测试文档

## 📝 文档规范

### 新增文档
所有新的文档文件都应该放在此目录下，按照以下命名规范：
- 使用大写字母和下划线命名
- 以 `.md` 扩展名结尾
- 文件名应该清楚描述文档内容

### 文档格式
- 使用 Markdown 格式
- 包含清晰的标题和目录
- 使用适当的 emoji 增强可读性
- 包含创建/更新时间信息

---

**最后更新**: 2025-08-01  
**维护者**: ToolMaster 开发团队
