# 分类结构更新指南

## 问题说明

如果您刷新页面后没有看到新增的分类（如"调试测试"、"多媒体设计"、"转换工具"等），这是因为分类数据存储在Supabase数据库中，而我们只修改了代码中的默认分类结构，没有更新数据库中的实际数据。

## 解决方案

### 方案1：自动检测更新（推荐）

项目现在包含了自动检测分类结构变化的功能。当您下次启动项目时：

1. 系统会自动检测分类结构是否有变化
2. 如果检测到变化，会自动更新数据库中的分类数据
3. 您会看到一个提示："分类结构已更新"

### 方案2：手动强制更新

如果自动更新没有触发，您可以手动强制更新：

1. 启动项目：`npm run dev`
2. 打开浏览器访问项目
3. 打开浏览器开发者工具（F12）
4. 在控制台（Console）中输入以下命令：

```javascript
forceUpdateCategories()
```

5. 按回车执行
6. 您会看到提示："分类结构已强制更新"
7. 刷新页面即可看到新的分类结构

### 方案3：通过分类管理器更新

1. 打开项目
2. 点击右上角的"数据管理"按钮
3. 在弹出的对话框中点击"分类管理"
4. 手动添加缺失的分类

## 新增的分类结构

### 开发工具 (development)
- **调试测试** (debugging)
  - 调试器 (debugger)
  - 单元测试 (unit-testing)
  - 集成测试 (integration-testing)
  - 性能测试 (performance-testing)

### 设计工具 (design)
- **多媒体设计** (multimedia)
  - 视频编辑 (video-editing)
  - 音频编辑 (audio-editing)
  - 动态图形 (motion-graphics)
  - 演示设计 (presentation)

### 其他工具 (other)
- **转换工具** (converter)
  - 格式转换 (format-converter)
  - 编码转换 (encoding-converter)
  - 时间转换 (time-converter)
  - 货币转换 (currency-converter)

### 所有分类的第4个三级分类
每个二级分类现在都有4个三级分类，具体请查看代码中的 `defaultCategories` 数组。

## 验证更新是否成功

更新完成后，您应该能看到：

1. **开发工具** 下有6个二级分类（包括新的"调试测试"）
2. **设计工具** 下有6个二级分类（包括新的"多媒体设计"）
3. **其他工具** 下有4个二级分类（包括新的"转换工具"）
4. 每个二级分类下都有4个三级分类

## 注意事项

- 更新分类结构不会影响现有的工具数据
- 如果您有自定义的分类，建议先备份
- 更新后，AI批量导入的分类准确性会显著提升

## 技术说明

项目现在包含以下改进：

1. **自动检测机制**：`checkIfCategoriesNeedUpdate()` 函数会比较当前分类和默认分类的结构
2. **强制更新功能**：`forceUpdateCategories()` 函数可以手动触发更新
3. **混合存储同步**：确保本地缓存和云端数据库的一致性

如果您遇到任何问题，请检查浏览器控制台的错误信息。
