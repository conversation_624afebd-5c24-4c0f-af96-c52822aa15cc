// 分类修正服务 - 智能默认分类和异步分类修正
import { Tool, Category, AIAnalysisResult } from './types'
import { AIService } from './ai-service'
import { ExtendedDatabase } from './database-extended'

export interface CategoryCorrection {
  toolId: string
  oldCategory: string
  oldSubcategory: string
  oldSubsubcategory: string
  newCategory: string
  newSubcategory: string
  newSubsubcategory: string
  confidence: number
  correctionReason: string
}

export interface SmartCategoryResult {
  category: string
  subcategory: string
  subsubcategory: string
  confidence: number
  method: 'keyword' | 'url' | 'default'
}

export class CategoryCorrectionService {
  // 关键词分类映射表
  private static readonly KEYWORD_MAPPING = {
    development: {
      keywords: ['code', 'git', 'github', 'api', 'developer', 'programming', 'coding', 'ide', 'editor', 'terminal', 'cli', 'sdk', 'framework', 'library', 'repository', 'commit', 'pull request', 'merge', 'branch', 'version control'],
      subcategories: {
        'code-editor': ['editor', 'ide', 'coding', 'syntax', 'highlight', 'autocomplete', 'intellisense'],
        'version-control': ['git', 'github', 'gitlab', 'bitbucket', 'svn', 'commit', 'branch', 'merge', 'pull request'],
        'api-tools': ['api', 'rest', 'graphql', 'postman', 'swagger', 'endpoint', 'testing', 'mock'],
        'database': ['database', 'sql', 'mysql', 'postgresql', 'mongodb', 'redis', 'query', 'schema'],
        'deployment': ['deploy', 'ci', 'cd', 'docker', 'kubernetes', 'aws', 'azure', 'vercel', 'netlify']
      }
    },
    design: {
      keywords: ['design', 'ui', 'ux', 'figma', 'sketch', 'adobe', 'photoshop', 'illustrator', 'color', 'palette', 'font', 'typography', 'icon', 'logo', 'brand', 'prototype', 'wireframe', 'mockup'],
      subcategories: {
        'ui-design': ['ui', 'interface', 'figma', 'sketch', 'prototype', 'wireframe', 'mockup'],
        'graphic-design': ['graphic', 'photoshop', 'illustrator', 'logo', 'brand', 'poster', 'banner'],
        'color-tools': ['color', 'palette', 'gradient', 'picker', 'scheme', 'hex', 'rgb']
      }
    },
    productivity: {
      keywords: ['note', 'task', 'todo', 'project', 'team', 'collaboration', 'document', 'file', 'storage', 'sync', 'organize', 'plan', 'schedule', 'calendar', 'reminder', 'workflow'],
      subcategories: {
        'note-taking': ['note', 'notebook', 'markdown', 'wiki', 'knowledge', 'obsidian', 'notion'],
        'task-management': ['task', 'todo', 'project', 'kanban', 'scrum', 'agile', 'trello', 'asana'],
        'file-management': ['file', 'storage', 'cloud', 'sync', 'backup', 'drive', 'dropbox']
      }
    },
    marketing: {
      keywords: ['seo', 'marketing', 'social', 'email', 'campaign', 'analytics', 'traffic', 'conversion', 'ads', 'advertising', 'promotion', 'brand', 'content'],
      subcategories: {
        'seo-tools': ['seo', 'search', 'keyword', 'rank', 'google', 'optimization', 'meta'],
        'social-media': ['social', 'facebook', 'twitter', 'instagram', 'linkedin', 'post', 'schedule'],
        'email-marketing': ['email', 'newsletter', 'campaign', 'mailchimp', 'automation', 'template']
      }
    },
    analytics: {
      keywords: ['analytics', 'data', 'statistics', 'chart', 'graph', 'dashboard', 'report', 'metrics', 'kpi', 'tracking', 'measurement', 'insight'],
      subcategories: {
        'web-analytics': ['web', 'website', 'traffic', 'visitor', 'pageview', 'bounce', 'conversion'],
        'business-intelligence': ['business', 'intelligence', 'dashboard', 'report', 'kpi', 'metrics'],
        'survey-tools': ['survey', 'poll', 'questionnaire', 'feedback', 'form', 'response']
      }
    },
    communication: {
      keywords: ['chat', 'message', 'email', 'video', 'call', 'meeting', 'conference', 'collaboration', 'team', 'slack', 'discord', 'zoom', 'teams'],
      subcategories: {
        'messaging': ['chat', 'message', 'instant', 'slack', 'discord', 'telegram', 'whatsapp'],
        'email-tools': ['email', 'mail', 'gmail', 'outlook', 'thunderbird', 'client'],
        'collaboration': ['collaborate', 'team', 'share', 'document', 'real-time', 'whiteboard']
      }
    },
    entertainment: {
      keywords: ['game', 'gaming', 'video', 'music', 'streaming', 'media', 'player', 'entertainment', 'fun', 'social', 'community', 'forum'],
      subcategories: {
        'media-streaming': ['video', 'music', 'streaming', 'netflix', 'youtube', 'spotify', 'podcast'],
        'gaming': ['game', 'gaming', 'steam', 'play', 'console', 'mobile'],
        'social-platform': ['social', 'community', 'forum', 'reddit', 'discord', 'facebook']
      }
    },
    education: {
      keywords: ['education', 'learning', 'course', 'tutorial', 'training', 'skill', 'language', 'study', 'teach', 'academic', 'university', 'school'],
      subcategories: {
        'online-courses': ['course', 'online', 'udemy', 'coursera', 'edx', 'training', 'certification'],
        'language-learning': ['language', 'english', 'translate', 'dictionary', 'duolingo', 'babel'],
        'reference': ['reference', 'dictionary', 'encyclopedia', 'wiki', 'academic', 'research']
      }
    },
    other: {
      keywords: ['tool', 'utility', 'calculator', 'converter', 'generator', 'helper', 'misc', 'various'],
      subcategories: {
        'utility': ['utility', 'tool', 'helper', 'misc', 'various', 'general'],
        'lifestyle': ['lifestyle', 'health', 'fitness', 'travel', 'food', 'recipe', 'personal'],
        'finance': ['finance', 'money', 'budget', 'investment', 'crypto', 'bank', 'payment']
      }
    }
  }

  // URL域名分类映射
  private static readonly DOMAIN_MAPPING: Record<string, SmartCategoryResult> = {
    'github.com': { category: 'development', subcategory: 'version-control', subsubcategory: 'git-tools', confidence: 0.95, method: 'url' },
    'gitlab.com': { category: 'development', subcategory: 'version-control', subsubcategory: 'git-tools', confidence: 0.95, method: 'url' },
    'figma.com': { category: 'design', subcategory: 'ui-design', subsubcategory: 'design-software', confidence: 0.95, method: 'url' },
    'sketch.com': { category: 'design', subcategory: 'ui-design', subsubcategory: 'design-software', confidence: 0.95, method: 'url' },
    'notion.so': { category: 'productivity', subcategory: 'note-taking', subsubcategory: 'note-app', confidence: 0.95, method: 'url' },
    'trello.com': { category: 'productivity', subcategory: 'task-management', subsubcategory: 'project-management', confidence: 0.95, method: 'url' },
    'slack.com': { category: 'communication', subcategory: 'messaging', subsubcategory: 'team-communication', confidence: 0.95, method: 'url' },
    'discord.com': { category: 'communication', subcategory: 'messaging', subsubcategory: 'chat-app', confidence: 0.95, method: 'url' },
    'youtube.com': { category: 'entertainment', subcategory: 'media-streaming', subsubcategory: 'video-streaming', confidence: 0.95, method: 'url' },
    'spotify.com': { category: 'entertainment', subcategory: 'media-streaming', subsubcategory: 'music-streaming', confidence: 0.95, method: 'url' }
  }

  /**
   * 智能默认分类 - 基于关键词和URL分析
   */
  static getSmartDefaultCategory(toolName: string, description: string, url: string): SmartCategoryResult {
    try {
      // 1. 首先尝试URL域名匹配
      const urlResult = this.getCategoryByUrl(url)
      if (urlResult.confidence > 0.8) {
        return urlResult
      }

      // 2. 基于关键词分析
      const keywordResult = this.getCategoryByKeywords(toolName, description)
      if (keywordResult.confidence > 0.6) {
        return keywordResult
      }

      // 3. 返回默认分类
      return {
        category: 'other',
        subcategory: 'utility',
        subsubcategory: 'calculator',
        confidence: 0.3,
        method: 'default'
      }
    } catch (error) {
      console.error('智能分类失败:', error)
      return {
        category: 'other',
        subcategory: 'utility',
        subsubcategory: 'calculator',
        confidence: 0.1,
        method: 'default'
      }
    }
  }

  /**
   * 基于URL域名进行分类
   */
  private static getCategoryByUrl(url: string): SmartCategoryResult {
    try {
      const urlObj = new URL(url)
      const domain = urlObj.hostname.replace('www.', '').toLowerCase()
      
      const mapping = this.DOMAIN_MAPPING[domain]
      if (mapping) {
        return mapping
      }

      // 检查子域名或相似域名
      for (const [knownDomain, result] of Object.entries(this.DOMAIN_MAPPING)) {
        if (domain.includes(knownDomain.split('.')[0]) || knownDomain.includes(domain.split('.')[0])) {
          return { ...result, confidence: result.confidence * 0.8 }
        }
      }

      return {
        category: 'other',
        subcategory: 'utility',
        subsubcategory: 'calculator',
        confidence: 0.2,
        method: 'url'
      }
    } catch (error) {
      return {
        category: 'other',
        subcategory: 'utility',
        subsubcategory: 'calculator',
        confidence: 0.1,
        method: 'url'
      }
    }
  }

  /**
   * 基于关键词进行分类
   */
  private static getCategoryByKeywords(toolName: string, description: string): SmartCategoryResult {
    const text = `${toolName} ${description}`.toLowerCase()
    let bestMatch: SmartCategoryResult = {
      category: 'other',
      subcategory: 'utility',
      subsubcategory: 'calculator',
      confidence: 0.1,
      method: 'keyword'
    }

    for (const [categoryId, categoryData] of Object.entries(this.KEYWORD_MAPPING)) {
      // 检查主分类关键词
      const categoryScore = this.calculateKeywordScore(text, categoryData.keywords)
      
      if (categoryScore > bestMatch.confidence) {
        // 寻找最佳子分类
        let bestSubcategory = 'utility'
        let bestSubsubcategory = 'calculator'
        let bestSubScore = 0

        for (const [subcategoryId, subcategoryKeywords] of Object.entries(categoryData.subcategories)) {
          const subScore = this.calculateKeywordScore(text, subcategoryKeywords)
          if (subScore > bestSubScore) {
            bestSubScore = subScore
            bestSubcategory = subcategoryId
            // 根据子分类设置默认的子子分类
            bestSubsubcategory = this.getDefaultSubsubcategory(categoryId, subcategoryId)
          }
        }

        bestMatch = {
          category: categoryId,
          subcategory: bestSubcategory,
          subsubcategory: bestSubsubcategory,
          confidence: Math.min(categoryScore + bestSubScore * 0.5, 0.9),
          method: 'keyword'
        }
      }
    }

    return bestMatch
  }

  /**
   * 计算关键词匹配分数
   */
  private static calculateKeywordScore(text: string, keywords: string[]): number {
    let score = 0
    let matches = 0

    for (const keyword of keywords) {
      if (text.includes(keyword.toLowerCase())) {
        matches++
        // 完整单词匹配得分更高
        const regex = new RegExp(`\\b${keyword.toLowerCase()}\\b`)
        if (regex.test(text)) {
          score += 0.1
        } else {
          score += 0.05
        }
      }
    }

    // 匹配率加成
    const matchRate = matches / keywords.length
    return Math.min(score + matchRate * 0.3, 1.0)
  }

  /**
   * 获取默认的子子分类
   */
  private static getDefaultSubsubcategory(category: string, subcategory: string): string {
    const defaults: Record<string, Record<string, string>> = {
      development: {
        'code-editor': 'online-editor',
        'version-control': 'git-tools',
        'api-tools': 'api-testing',
        'database': 'db-client',
        'deployment': 'ci-cd'
      },
      design: {
        'ui-design': 'design-software',
        'graphic-design': 'image-editor',
        'color-tools': 'color-picker'
      },
      productivity: {
        'note-taking': 'note-app',
        'task-management': 'project-management',
        'file-management': 'cloud-storage'
      },
      marketing: {
        'seo-tools': 'keyword-research',
        'social-media': 'social-management',
        'email-marketing': 'email-builder'
      },
      analytics: {
        'web-analytics': 'traffic-analysis',
        'business-intelligence': 'dashboard',
        'survey-tools': 'survey-builder'
      },
      communication: {
        'messaging': 'chat-app',
        'email-tools': 'email-client',
        'collaboration': 'document-sharing'
      },
      entertainment: {
        'media-streaming': 'video-streaming',
        'gaming': 'online-games',
        'social-platform': 'social-network'
      },
      education: {
        'online-courses': 'course-platform',
        'language-learning': 'language-app',
        'reference': 'dictionary'
      },
      other: {
        'utility': 'calculator',
        'lifestyle': 'health-fitness',
        'finance': 'budgeting'
      }
    }

    return defaults[category]?.[subcategory] || 'calculator'
  }

  /**
   * 异步修正工具分类
   */
  static async correctToolCategories(tools: Tool[]): Promise<CategoryCorrection[]> {
    const corrections: CategoryCorrection[] = []
    
    try {
      for (const tool of tools) {
        // 只修正默认分类或低置信度分类
        if (this.needsCorrection(tool)) {
          const correction = await this.correctSingleTool(tool)
          if (correction) {
            corrections.push(correction)
          }
        }
      }
    } catch (error) {
      console.error('批量分类修正失败:', error)
    }

    return corrections
  }

  /**
   * 判断工具是否需要分类修正
   */
  private static needsCorrection(tool: Tool): boolean {
    // 如果是默认分类，需要修正
    if (tool.category === 'other' && 
        tool.subcategory === 'utility' && 
        tool.subsubcategory === 'calculator') {
      return true
    }

    // 如果分类看起来不合理，也需要修正
    // 这里可以添加更多的判断逻辑
    return false
  }

  /**
   * 修正单个工具的分类
   */
  private static async correctSingleTool(tool: Tool): Promise<CategoryCorrection | null> {
    try {
      // 使用完整的AI分析重新分类
      const analysisResult = await AIService.analyzeUrl(tool.url, false) // 不使用缓存，确保重新分析
      
      if (analysisResult && 
          (analysisResult.category !== tool.category ||
           analysisResult.subcategory !== tool.subcategory ||
           analysisResult.subsubcategory !== tool.subsubcategory)) {
        
        return {
          toolId: tool.id,
          oldCategory: tool.category,
          oldSubcategory: tool.subcategory,
          oldSubsubcategory: tool.subsubcategory,
          newCategory: analysisResult.category,
          newSubcategory: analysisResult.subcategory,
          newSubsubcategory: analysisResult.subsubcategory,
          confidence: analysisResult.confidence || 0.8,
          correctionReason: '基于AI重新分析的分类修正'
        }
      }

      return null
    } catch (error) {
      console.error(`修正工具 ${tool.name} 分类失败:`, error)
      return null
    }
  }

  /**
   * 应用分类修正到数据库
   */
  static async applyCorrections(corrections: CategoryCorrection[]): Promise<void> {
    try {
      for (const correction of corrections) {
        // 这里需要调用数据库更新方法
        // 由于我们不想修改现有的数据库服务，这里先记录日志
        console.log('应用分类修正:', correction)
        
        // 记录修正操作到日志
        await ExtendedDatabase.createOperationLog(
          '异步分类修正',
          'update',
          'tool',
          correction.toolId,
          {
            oldCategory: `${correction.oldCategory}/${correction.oldSubcategory}/${correction.oldSubsubcategory}`,
            newCategory: `${correction.newCategory}/${correction.newSubcategory}/${correction.newSubsubcategory}`,
            confidence: correction.confidence,
            reason: correction.correctionReason
          },
          'success'
        )
      }
    } catch (error) {
      console.error('应用分类修正失败:', error)
      throw error
    }
  }
}
