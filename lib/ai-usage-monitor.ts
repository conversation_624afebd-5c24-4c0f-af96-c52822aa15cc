import { supabase } from './supabase'

// AI 使用监控相关类型定义
export interface AIUsageLog {
  id?: number
  api_type: 'deep-search' | 'global-search' | 'analyze-url' | 'add-tool'
  model: string
  input_tokens: number
  output_tokens: number
  total_tokens: number
  cost_yuan: number
  is_cache_hit: boolean
  is_discount_period: boolean
  response_time_ms: number
  success: boolean
  error_message?: string
  request_id?: string
  user_query?: string
  created_at?: string
}

export interface AIUsageDailySummary {
  date_only: string
  total_calls: number
  successful_calls: number
  failed_calls: number
  success_rate: number
  total_input_tokens: number
  total_output_tokens: number
  total_tokens: number
  total_cost_yuan: number
  cache_hit_cost_yuan: number
  discount_cost_yuan: number
  avg_response_time_ms: number
  max_response_time_ms: number
  api_type_stats: Record<string, any>
}

export interface AIUsageMonthlySummary {
  year_month: string
  total_calls: number
  successful_calls: number
  failed_calls: number
  success_rate: number
  total_input_tokens: number
  total_output_tokens: number
  total_tokens: number
  total_cost_yuan: number
  avg_daily_cost_yuan: number
  daily_cost_distribution: Record<string, number>
  api_type_stats: Record<string, any>
}

// DeepSeek 价格配置（每百万 tokens）
export const DEEPSEEK_PRICING = {
  'deepseek-chat': {
    standard: {
      input_cache_hit: 0.5,      // 缓存命中
      input_cache_miss: 2.0,     // 缓存未命中
      output: 8.0
    },
    discount: {
      input_cache_hit: 0.25,     // 5折
      input_cache_miss: 1.0,     // 5折
      output: 4.0                // 5折
    }
  },
  'deepseek-reasoner': {
    standard: {
      input_cache_hit: 1.0,
      input_cache_miss: 4.0,
      output: 16.0
    },
    discount: {
      input_cache_hit: 0.25,     // 2.5折
      input_cache_miss: 1.0,     // 2.5折
      output: 4.0                // 2.5折
    }
  }
} as const

/**
 * AI 使用监控服务
 * 负责记录和统计 AI API 的使用情况和费用
 */
export class AIUsageMonitor {
  /**
   * 判断是否为优惠时段（北京时间 00:30-08:30）
   */
  private static isDiscountPeriod(): boolean {
    const now = new Date()
    // 转换为北京时间（UTC+8）
    const beijingTime = new Date(now.getTime() + (8 * 60 * 60 * 1000))
    const hour = beijingTime.getUTCHours()
    const minute = beijingTime.getUTCMinutes()
    
    // 00:30-08:30 为优惠时段
    return (hour === 0 && minute >= 30) || (hour >= 1 && hour < 8) || (hour === 8 && minute < 30)
  }

  /**
   * 计算 API 调用费用
   */
  private static calculateCost(
    inputTokens: number,
    outputTokens: number,
    model: string = 'deepseek-chat',
    isCacheHit: boolean = false,
    isDiscountPeriod?: boolean
  ): number {
    const pricing = DEEPSEEK_PRICING[model as keyof typeof DEEPSEEK_PRICING]
    if (!pricing) {
      console.warn(`未知模型 ${model}，使用默认价格`)
      return 0
    }

    const isDiscount = isDiscountPeriod ?? this.isDiscountPeriod()
    const rates = isDiscount ? pricing.discount : pricing.standard
    
    // 计算输入 tokens 费用
    const inputRate = isCacheHit ? rates.input_cache_hit : rates.input_cache_miss
    const inputCost = (inputTokens / 1000000) * inputRate
    
    // 计算输出 tokens 费用
    const outputCost = (outputTokens / 1000000) * rates.output
    
    return Number((inputCost + outputCost).toFixed(4))
  }

  /**
   * 记录 AI API 使用情况
   */
  static async logUsage(params: {
    apiType: AIUsageLog['api_type']
    model?: string
    inputTokens: number
    outputTokens: number
    responseTimeMs: number
    success: boolean
    errorMessage?: string
    requestId?: string
    userQuery?: string
    isCacheHit?: boolean
  }): Promise<void> {
    try {
      const {
        apiType,
        model = 'deepseek-chat',
        inputTokens,
        outputTokens,
        responseTimeMs,
        success,
        errorMessage,
        requestId,
        userQuery,
        isCacheHit = false
      } = params

      const totalTokens = inputTokens + outputTokens
      const isDiscountPeriod = this.isDiscountPeriod()
      const cost = this.calculateCost(inputTokens, outputTokens, model, isCacheHit, isDiscountPeriod)

      const logData: Omit<AIUsageLog, 'id' | 'created_at'> = {
        api_type: apiType,
        model,
        input_tokens: inputTokens,
        output_tokens: outputTokens,
        total_tokens: totalTokens,
        cost_yuan: cost,
        is_cache_hit: isCacheHit,
        is_discount_period: isDiscountPeriod,
        response_time_ms: responseTimeMs,
        success,
        error_message: errorMessage,
        request_id: requestId,
        user_query: userQuery ? userQuery.substring(0, 500) : undefined // 限制长度，保护隐私
      }

      // 异步写入数据库，不阻塞主流程
      setImmediate(async () => {
        try {
          const { error } = await supabase
            .from('ai_usage_logs')
            .insert([logData])

          if (error) {
            console.error('记录 AI 使用日志失败:', error)
          } else {
            console.log(`AI 使用日志已记录: ${apiType}, tokens: ${totalTokens}, 费用: ¥${cost}`)
          }

          // 触发每日汇总更新
          await this.updateDailySummary()
        } catch (err) {
          console.error('AI 使用监控异步处理失败:', err)
        }
      })
    } catch (error) {
      console.error('记录 AI 使用情况失败:', error)
      // 不抛出错误，避免影响主业务流程
    }
  }

  /**
   * 更新每日汇总统计
   */
  private static async updateDailySummary(): Promise<void> {
    try {
      const today = new Date().toISOString().split('T')[0]

      // 查询今日的使用统计
      const { data: todayLogs, error } = await supabase
        .from('ai_usage_logs')
        .select('*')
        .eq('date_only', today)

      if (error) {
        console.error('查询今日 AI 使用日志失败:', error)
        return
      }

      if (!todayLogs || todayLogs.length === 0) {
        return
      }

      // 计算汇总数据
      const summary = this.calculateDailySummary(todayLogs)

      // 更新或插入每日汇总
      const { error: upsertError } = await supabase
        .from('ai_usage_daily_summary')
        .upsert([{
          date_only: today,
          ...summary
        }], {
          onConflict: 'date_only'
        })

      if (upsertError) {
        console.error('更新每日汇总失败:', upsertError)
      }
    } catch (error) {
      console.error('更新每日汇总统计失败:', error)
    }
  }

  /**
   * 计算每日汇总数据
   */
  private static calculateDailySummary(logs: AIUsageLog[]): Omit<AIUsageDailySummary, 'date_only'> {
    const totalCalls = logs.length
    const successfulCalls = logs.filter(log => log.success).length
    const failedCalls = totalCalls - successfulCalls
    const successRate = totalCalls > 0 ? Number(((successfulCalls / totalCalls) * 100).toFixed(2)) : 0

    const totalInputTokens = logs.reduce((sum, log) => sum + log.input_tokens, 0)
    const totalOutputTokens = logs.reduce((sum, log) => sum + log.output_tokens, 0)
    const totalTokens = totalInputTokens + totalOutputTokens

    const totalCostYuan = logs.reduce((sum, log) => sum + log.cost_yuan, 0)
    const cacheHitCostYuan = logs.filter(log => log.is_cache_hit).reduce((sum, log) => sum + log.cost_yuan, 0)
    const discountCostYuan = logs.filter(log => log.is_discount_period).reduce((sum, log) => sum + log.cost_yuan, 0)

    const responseTimes = logs.map(log => log.response_time_ms)
    const avgResponseTimeMs = responseTimes.length > 0 ? Math.round(responseTimes.reduce((sum, time) => sum + time, 0) / responseTimes.length) : 0
    const maxResponseTimeMs = responseTimes.length > 0 ? Math.max(...responseTimes) : 0

    // 按 API 类型分组统计
    const apiTypeStats: Record<string, any> = {}
    logs.forEach(log => {
      if (!apiTypeStats[log.api_type]) {
        apiTypeStats[log.api_type] = {
          calls: 0,
          tokens: 0,
          cost: 0,
          success_rate: 0
        }
      }
      apiTypeStats[log.api_type].calls++
      apiTypeStats[log.api_type].tokens += log.total_tokens
      apiTypeStats[log.api_type].cost += log.cost_yuan
    })

    // 计算各 API 类型的成功率
    Object.keys(apiTypeStats).forEach(apiType => {
      const apiLogs = logs.filter(log => log.api_type === apiType)
      const successCount = apiLogs.filter(log => log.success).length
      apiTypeStats[apiType].success_rate = Number(((successCount / apiLogs.length) * 100).toFixed(2))
    })

    return {
      total_calls: totalCalls,
      successful_calls: successfulCalls,
      failed_calls: failedCalls,
      success_rate: successRate,
      total_input_tokens: totalInputTokens,
      total_output_tokens: totalOutputTokens,
      total_tokens: totalTokens,
      total_cost_yuan: Number(totalCostYuan.toFixed(4)),
      cache_hit_cost_yuan: Number(cacheHitCostYuan.toFixed(4)),
      discount_cost_yuan: Number(discountCostYuan.toFixed(4)),
      avg_response_time_ms: avgResponseTimeMs,
      max_response_time_ms: maxResponseTimeMs,
      api_type_stats: apiTypeStats
    }
  }

  /**
   * 获取今日使用统计
   */
  static async getTodayUsage(): Promise<AIUsageDailySummary | null> {
    try {
      const today = new Date().toISOString().split('T')[0]

      const { data, error } = await supabase
        .from('ai_usage_daily_summary')
        .select('*')
        .eq('date_only', today)
        .single()

      if (error && error.code !== 'PGRST116') { // PGRST116 是 "not found" 错误
        console.error('获取今日使用统计失败:', error)
        return null
      }

      return data || null
    } catch (error) {
      console.error('获取今日使用统计失败:', error)
      return null
    }
  }

  /**
   * 获取本月使用统计
   */
  static async getMonthlyUsage(): Promise<AIUsageMonthlySummary | null> {
    try {
      const yearMonth = new Date().toISOString().substring(0, 7) // YYYY-MM

      const { data, error } = await supabase
        .from('ai_usage_monthly_summary')
        .select('*')
        .eq('year_month', yearMonth)
        .single()

      if (error && error.code !== 'PGRST116') {
        console.error('获取本月使用统计失败:', error)
        return null
      }

      return data || null
    } catch (error) {
      console.error('获取本月使用统计失败:', error)
      return null
    }
  }

  /**
   * 获取最近几天的使用趋势
   */
  static async getRecentUsageTrend(days: number = 7): Promise<AIUsageDailySummary[]> {
    try {
      const { data, error } = await supabase
        .from('ai_usage_daily_summary')
        .select('*')
        .order('date_only', { ascending: false })
        .limit(days)

      if (error) {
        console.error('获取使用趋势失败:', error)
        return []
      }

      return data || []
    } catch (error) {
      console.error('获取使用趋势失败:', error)
      return []
    }
  }
}
