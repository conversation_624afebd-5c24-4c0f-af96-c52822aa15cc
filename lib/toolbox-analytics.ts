/**
 * 工具箱使用分析和统计
 */

interface ToolboxUsageEvent {
  toolId: string
  action: 'open' | 'analyze' | 'export' | 'import' | 'copy'
  url?: string
  resultCount?: number
  processingTime?: number
  success: boolean
  timestamp: number
}

interface ToolboxStats {
  totalUsage: number
  successfulExtractions: number
  totalToolsExtracted: number
  averageProcessingTime: number
  mostUsedFeatures: Record<string, number>
  dailyUsage: Record<string, number>
}

class ToolboxAnalytics {
  private static readonly STORAGE_KEY = 'toolmaster-toolbox-analytics'
  private static readonly MAX_EVENTS = 1000

  /**
   * 记录工具箱使用事件
   */
  static logEvent(event: Omit<ToolboxUsageEvent, 'timestamp'>): void {
    try {
      const events = this.getEvents()
      const newEvent: ToolboxUsageEvent = {
        ...event,
        timestamp: Date.now()
      }

      events.push(newEvent)

      // 限制事件数量，保留最新的事件
      if (events.length > this.MAX_EVENTS) {
        events.splice(0, events.length - this.MAX_EVENTS)
      }

      localStorage.setItem(this.STORAGE_KEY, JSON.stringify(events))
    } catch (error) {
      console.warn('记录工具箱使用事件失败:', error)
    }
  }

  /**
   * 获取所有使用事件
   */
  static getEvents(): ToolboxUsageEvent[] {
    try {
      const data = localStorage.getItem(this.STORAGE_KEY)
      return data ? JSON.parse(data) : []
    } catch (error) {
      console.warn('获取工具箱使用事件失败:', error)
      return []
    }
  }

  /**
   * 获取使用统计
   */
  static getStats(days: number = 30): ToolboxStats {
    const events = this.getEvents()
    const cutoffTime = Date.now() - (days * 24 * 60 * 60 * 1000)
    const recentEvents = events.filter(event => event.timestamp > cutoffTime)

    const stats: ToolboxStats = {
      totalUsage: recentEvents.length,
      successfulExtractions: 0,
      totalToolsExtracted: 0,
      averageProcessingTime: 0,
      mostUsedFeatures: {},
      dailyUsage: {}
    }

    let totalProcessingTime = 0
    let processingTimeCount = 0

    recentEvents.forEach(event => {
      // 统计成功的提取
      if (event.action === 'analyze' && event.success) {
        stats.successfulExtractions++
        if (event.resultCount) {
          stats.totalToolsExtracted += event.resultCount
        }
      }

      // 统计处理时间
      if (event.processingTime) {
        totalProcessingTime += event.processingTime
        processingTimeCount++
      }

      // 统计功能使用频率
      const featureKey = `${event.toolId}-${event.action}`
      stats.mostUsedFeatures[featureKey] = (stats.mostUsedFeatures[featureKey] || 0) + 1

      // 统计每日使用量
      const dateKey = new Date(event.timestamp).toISOString().split('T')[0]
      stats.dailyUsage[dateKey] = (stats.dailyUsage[dateKey] || 0) + 1
    })

    // 计算平均处理时间
    if (processingTimeCount > 0) {
      stats.averageProcessingTime = Math.round(totalProcessingTime / processingTimeCount)
    }

    return stats
  }

  /**
   * 获取今日统计
   */
  static getTodayStats(): {
    extractions: number
    toolsExtracted: number
    averageTime: number
    successRate: number
  } {
    const today = new Date().toISOString().split('T')[0]
    const events = this.getEvents()
    const todayEvents = events.filter(event => {
      const eventDate = new Date(event.timestamp).toISOString().split('T')[0]
      return eventDate === today
    })

    const analyzeEvents = todayEvents.filter(event => event.action === 'analyze')
    const successfulEvents = analyzeEvents.filter(event => event.success)
    
    const totalTools = successfulEvents.reduce((sum, event) => sum + (event.resultCount || 0), 0)
    const totalTime = successfulEvents.reduce((sum, event) => sum + (event.processingTime || 0), 0)
    
    return {
      extractions: analyzeEvents.length,
      toolsExtracted: totalTools,
      averageTime: successfulEvents.length > 0 ? Math.round(totalTime / successfulEvents.length) : 0,
      successRate: analyzeEvents.length > 0 ? Math.round((successfulEvents.length / analyzeEvents.length) * 100) : 0
    }
  }

  /**
   * 获取热门域名统计
   */
  static getPopularDomains(limit: number = 10): Array<{ domain: string; count: number }> {
    const events = this.getEvents()
    const domainCounts: Record<string, number> = {}

    events.forEach(event => {
      if (event.url && event.action === 'analyze' && event.success) {
        try {
          const domain = new URL(event.url).hostname
          domainCounts[domain] = (domainCounts[domain] || 0) + 1
        } catch {
          // 忽略无效URL
        }
      }
    })

    return Object.entries(domainCounts)
      .map(([domain, count]) => ({ domain, count }))
      .sort((a, b) => b.count - a.count)
      .slice(0, limit)
  }

  /**
   * 清理旧数据
   */
  static cleanup(keepDays: number = 90): void {
    try {
      const events = this.getEvents()
      const cutoffTime = Date.now() - (keepDays * 24 * 60 * 60 * 1000)
      const filteredEvents = events.filter(event => event.timestamp > cutoffTime)
      
      localStorage.setItem(this.STORAGE_KEY, JSON.stringify(filteredEvents))
    } catch (error) {
      console.warn('清理工具箱分析数据失败:', error)
    }
  }

  /**
   * 导出统计数据
   */
  static exportStats(): string {
    const stats = this.getStats()
    const events = this.getEvents()
    
    return JSON.stringify({
      exportTime: new Date().toISOString(),
      stats,
      recentEvents: events.slice(-100) // 只导出最近100个事件
    }, null, 2)
  }
}

export { ToolboxAnalytics, type ToolboxUsageEvent, type ToolboxStats }
