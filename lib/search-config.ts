// 搜索功能配置
export interface SearchConfig {
  // 是否启用优化版搜索
  useOptimizedSearch: boolean
  
  // 深度搜索配置
  deepSearch: {
    enabled: boolean
    useOptimized: boolean
    timeout: number // 超时时间（毫秒）
    maxRetries: number // 最大重试次数
  }
  
  // 全网搜索配置
  globalSearch: {
    enabled: boolean
    useOptimized: boolean
    timeout: number // 超时时间（毫秒）
    maxRetries: number // 最大重试次数
  }
  
  // AI服务配置
  aiService: {
    maxTokens: {
      optimized: number
      original: number
    }
    temperature: {
      optimized: number
      original: number
    }
  }
  
  // 缓存配置
  cache: {
    enabled: boolean
    ttl: number // 缓存时间（毫秒）
  }
  
  // 异步分类修正配置
  asyncCorrection: {
    enabled: boolean
    delay: number // 延迟时间（毫秒）
  }
}

// 默认配置
export const DEFAULT_SEARCH_CONFIG: SearchConfig = {
  useOptimizedSearch: false, // 默认使用原版（更稳定）
  
  deepSearch: {
    enabled: true,
    useOptimized: false, // 默认使用原版
    timeout: 30000, // 30秒超时
    maxRetries: 2
  },
  
  globalSearch: {
    enabled: true,
    useOptimized: false, // 默认使用原版
    timeout: 45000, // 45秒超时
    maxRetries: 2
  },
  
  aiService: {
    maxTokens: {
      optimized: 1000,
      original: 2000
    },
    temperature: {
      optimized: 0.3,
      original: 0.7
    }
  },
  
  cache: {
    enabled: true,
    ttl: 300000 // 5分钟缓存
  },
  
  asyncCorrection: {
    enabled: true,
    delay: 100 // 100ms后开始异步修正
  }
}

// 获取当前搜索配置
export function getSearchConfig(): SearchConfig {
  // 可以从环境变量或数据库中读取配置
  const config = { ...DEFAULT_SEARCH_CONFIG }
  
  // 从环境变量覆盖配置
  if (process.env.USE_OPTIMIZED_SEARCH === 'true') {
    config.useOptimizedSearch = true
    config.deepSearch.useOptimized = true
    config.globalSearch.useOptimized = true
  }
  
  if (process.env.DEEP_SEARCH_TIMEOUT) {
    config.deepSearch.timeout = parseInt(process.env.DEEP_SEARCH_TIMEOUT)
  }
  
  if (process.env.GLOBAL_SEARCH_TIMEOUT) {
    config.globalSearch.timeout = parseInt(process.env.GLOBAL_SEARCH_TIMEOUT)
  }
  
  return config
}

// 更新搜索配置（运行时修改）
let runtimeConfig: SearchConfig | null = null

export function updateSearchConfig(newConfig: Partial<SearchConfig>): void {
  runtimeConfig = {
    ...getSearchConfig(),
    ...newConfig
  }
}

export function getCurrentSearchConfig(): SearchConfig {
  return runtimeConfig || getSearchConfig()
}

// 配置预设
export const SEARCH_CONFIG_PRESETS = {
  // 稳定模式（推荐生产环境）
  stable: {
    useOptimizedSearch: false,
    deepSearch: { useOptimized: false, timeout: 30000 },
    globalSearch: { useOptimized: false, timeout: 45000 }
  },
  
  // 快速模式（实验性）
  fast: {
    useOptimizedSearch: true,
    deepSearch: { useOptimized: true, timeout: 20000 },
    globalSearch: { useOptimized: true, timeout: 30000 }
  },
  
  // 混合模式（深度搜索用原版，全网搜索用优化版）
  hybrid: {
    useOptimizedSearch: false,
    deepSearch: { useOptimized: false, timeout: 30000 },
    globalSearch: { useOptimized: true, timeout: 30000 }
  }
}

// 应用配置预设
export function applyConfigPreset(preset: keyof typeof SEARCH_CONFIG_PRESETS): void {
  const presetConfig = SEARCH_CONFIG_PRESETS[preset]
  updateSearchConfig(presetConfig)
  console.log(`已应用搜索配置预设: ${preset}`)
}
