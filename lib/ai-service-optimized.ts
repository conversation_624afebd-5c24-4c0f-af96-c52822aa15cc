// 优化版AI服务 - 快速搜索和异步分类修正
import { <PERSON><PERSON>, DeepSearchResult, AIAnalysisResult } from './types'
import { CategoryCorrectionService, CategoryCorrection } from './category-correction'
import { getCurrentSearchConfig } from './search-config'

export interface OptimizedSearchResult extends DeepSearchResult {
  status: 'processing' | 'completed'
  searchId: string
  processingTime: number
}

export interface GlobalSearchTool {
  name: string
  url: string
  description: string
  tags: string[]
  category: string
  subcategory: string
  subsubcategory: string
  relevanceScore: number
  recommendReason: string
  isNewTool: boolean
}

export interface OptimizedGlobalSearchResult {
  searchSummary: string
  recommendedTools: GlobalSearchTool[]
  searchInsights: string
  status: 'processing' | 'completed'
  searchId: string
  processingTime: number
}

export class OptimizedAIService {
  private static readonly DEEPSEEK_API_URL = process.env.DEEPSEEK_API_URL || 'https://api.deepseek.com/chat/completions'
  private static readonly DEEPSEEK_API_KEY = process.env.DEEPSEEK_API_KEY

  /**
   * 带重试的AI调用
   */
  private static async callDeepSeekAPIWithRetry(
    systemPrompt: string,
    userPrompt: string,
    temperature: number = 0.3,
    maxTokens: number = 1000,
    timeout: number = 30000,
    maxRetries: number = 2
  ): Promise<string> {
    let lastError: Error | null = null

    for (let attempt = 1; attempt <= maxRetries + 1; attempt++) {
      try {
        console.log(`DeepSeek API 调用尝试 ${attempt}/${maxRetries + 1}`)
        return await this.callDeepSeekAPI(systemPrompt, userPrompt, temperature, maxTokens, timeout)
      } catch (error) {
        lastError = error as Error
        console.error(`第 ${attempt} 次尝试失败:`, error)

        if (attempt <= maxRetries) {
          const delay = Math.min(1000 * Math.pow(2, attempt - 1), 5000) // 指数退避，最大5秒
          console.log(`等待 ${delay}ms 后重试...`)
          await new Promise(resolve => setTimeout(resolve, delay))
        }
      }
    }

    throw lastError || new Error('所有重试尝试都失败了')
  }

  /**
   * 调用 DeepSeek API - 优化版（带超时处理）
   */
  private static async callDeepSeekAPI(
    systemPrompt: string,
    userPrompt: string,
    temperature: number = 0.3,
    maxTokens: number = 1000,
    timeout: number = 30000
  ): Promise<string> {
    const startTime = Date.now()

    try {
      // 创建超时Promise
      const timeoutPromise = new Promise<never>((_, reject) => {
        setTimeout(() => {
          reject(new Error(`DeepSeek API 调用超时 (${timeout}ms)`))
        }, timeout)
      })

      // 创建API调用Promise
      const apiPromise = fetch(this.DEEPSEEK_API_URL, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${this.DEEPSEEK_API_KEY}`
        },
        body: JSON.stringify({
          model: 'deepseek-chat',
          messages: [
            { role: 'system', content: systemPrompt },
            { role: 'user', content: userPrompt }
          ],
          temperature,
          max_tokens: maxTokens,
          stream: false
        })
      })

      // 使用Promise.race实现超时控制
      const response = await Promise.race([apiPromise, timeoutPromise])

      if (!response.ok) {
        throw new Error(`DeepSeek API error: ${response.status} ${response.statusText}`)
      }

      const data = await response.json()
      const content = data.choices?.[0]?.message?.content

      if (!content) {
        throw new Error('DeepSeek API 返回空内容')
      }

      const processingTime = Date.now() - startTime
      console.log(`DeepSeek API 调用成功 (耗时: ${processingTime}ms)`)

      return content
    } catch (error) {
      const processingTime = Date.now() - startTime
      console.error(`DeepSeek API 调用失败 (耗时: ${processingTime}ms):`, error)
      throw error
    }
  }

  /**
   * 解析AI响应 - 增强版
   */
  private static parseAIResponse(aiResponse: string): any {
    try {
      console.log('原始AI响应:', aiResponse.substring(0, 500) + '...')

      // 尝试直接解析整个响应
      try {
        const parsed = JSON.parse(aiResponse)
        console.log('直接解析成功:', parsed)
        return parsed
      } catch (directParseError) {
        console.log('直接解析失败，尝试提取JSON')
      }

      // 尝试提取JSON对象
      const jsonMatch = aiResponse.match(/\{[\s\S]*\}/)
      if (jsonMatch) {
        const parsed = JSON.parse(jsonMatch[0])
        console.log('JSON提取解析成功:', parsed)
        return parsed
      }

      // 尝试提取JSON数组
      const arrayMatch = aiResponse.match(/\[[\s\S]*\]/)
      if (arrayMatch) {
        const parsed = JSON.parse(arrayMatch[0])
        console.log('数组提取解析成功:', parsed)
        return parsed
      }

      // 如果都失败了，尝试从代码块中提取
      const codeBlockMatch = aiResponse.match(/```(?:json)?\s*([\s\S]*?)\s*```/)
      if (codeBlockMatch) {
        const parsed = JSON.parse(codeBlockMatch[1])
        console.log('代码块提取解析成功:', parsed)
        return parsed
      }

      console.warn('无法提取有效JSON，返回原始响应')
      return { rawResponse: aiResponse }
    } catch (error) {
      console.error('解析AI响应失败:', error)
      console.error('原始响应:', aiResponse)
      return { rawResponse: aiResponse, parseError: true }
    }
  }

  /**
   * 快速深度搜索 - 精简版
   */
  static async quickDeepSearch(query: string, allTools: Tool[]): Promise<OptimizedSearchResult> {
    const searchId = `search_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`
    const startTime = Date.now()

    try {
      // 构建精简的工具数据
      const toolsSummary = allTools.map(tool => ({
        id: tool.id,
        name: tool.name,
        tags: tool.tags.slice(0, 3), // 只取前3个标签
        category: tool.category
      }))

      // 精简的系统提示词
      const systemPrompt = `你是工具推荐专家。根据用户查询，从工具库中推荐相关工具。

返回JSON格式：
{
  "searchSummary": "搜索总结",
  "recommendedTools": [
    {
      "id": "工具ID",
      "relevanceScore": 0.95,
      "recommendReason": "推荐理由"
    }
  ],
  "searchInsights": "搜索洞察"
}

要求：
1. 推荐3-8个最相关的工具
2. relevanceScore范围0.1-1.0
3. 必须从提供的工具库中选择
4. 即使相关性不高也要推荐一些工具`

      const userPrompt = `查询: ${query}

工具库 (${toolsSummary.length}个):
${toolsSummary.map(t => `${t.id}: ${t.name} [${t.tags.join(',')}] (${t.category})`).join('\n')}`

      console.log('快速深度搜索 - 查询:', query)
      console.log('快速深度搜索 - 工具数量:', toolsSummary.length)

      const searchConfig = getCurrentSearchConfig()
      const aiResponse = await this.callDeepSeekAPIWithRetry(
        systemPrompt,
        userPrompt,
        searchConfig.aiService.temperature.optimized,
        searchConfig.aiService.maxTokens.optimized,
        searchConfig.deepSearch.timeout,
        searchConfig.deepSearch.maxRetries
      )
      const result = this.parseAIResponse(aiResponse)

      // 处理AI返回结果 - 增强版
      let finalResult: OptimizedSearchResult

      console.log('处理AI返回结果，类型:', typeof result, '是否为数组:', Array.isArray(result))

      if (Array.isArray(result)) {
        // AI直接返回了推荐工具数组
        console.log('AI返回数组格式，长度:', result.length)
        finalResult = {
          searchSummary: `为您找到 ${result.length} 个相关工具`,
          recommendedTools: result,
          searchInsights: `基于查询分析，推荐了 ${result.length} 个相关工具`,
          status: 'processing',
          searchId,
          processingTime: Date.now() - startTime
        }
      } else if (result && typeof result === 'object') {
        // AI返回了对象格式
        console.log('AI返回对象格式，包含字段:', Object.keys(result))

        // 检查是否有推荐工具字段
        let recommendedTools = []
        if (result.recommendedTools && Array.isArray(result.recommendedTools)) {
          recommendedTools = result.recommendedTools
        } else if (result.tools && Array.isArray(result.tools)) {
          recommendedTools = result.tools
        } else if (result.results && Array.isArray(result.results)) {
          recommendedTools = result.results
        }

        finalResult = {
          searchSummary: result.searchSummary || result.summary || `为您找到 ${recommendedTools.length} 个相关工具`,
          recommendedTools: recommendedTools,
          searchInsights: result.searchInsights || result.insights || '基于查询分析的推荐结果',
          status: 'processing',
          searchId,
          processingTime: Date.now() - startTime
        }
      } else if (result && result.rawResponse) {
        // 解析失败，尝试从原始响应中提取信息
        console.log('解析失败，尝试从原始响应提取信息')
        finalResult = {
          searchSummary: '搜索完成，但AI响应格式异常',
          recommendedTools: [],
          searchInsights: '请尝试使用不同的关键词进行搜索',
          status: 'processing',
          searchId,
          processingTime: Date.now() - startTime
        }
      } else {
        // 完全无法处理的情况
        console.log('无法处理的AI响应格式')
        finalResult = {
          searchSummary: '搜索完成，但未找到合适的工具',
          recommendedTools: [],
          searchInsights: '请尝试使用不同的关键词进行搜索',
          status: 'processing',
          searchId,
          processingTime: Date.now() - startTime
        }
      }

      // 验证推荐工具的有效性
      finalResult.recommendedTools = finalResult.recommendedTools.filter(rec => {
        if (!rec || typeof rec !== 'object') return false
        if (!rec.id || typeof rec.relevanceScore !== 'number') return false
        return allTools.some(tool => tool.id === rec.id)
      })

      // 确保推荐理由存在
      finalResult.recommendedTools = finalResult.recommendedTools.map(rec => ({
        ...rec,
        recommendReason: rec.recommendReason || '相关工具推荐'
      }))

      console.log('快速深度搜索完成:', finalResult)
      return finalResult

    } catch (error) {
      console.error('快速深度搜索失败:', error)

      return {
        searchSummary: '搜索过程中出现错误',
        recommendedTools: [],
        searchInsights: '请稍后重试或使用不同的搜索关键词',
        status: 'processing',
        searchId,
        processingTime: Date.now() - startTime
      }
    }
  }

  /**
   * 快速全网搜索 - 精简版
   */
  static async quickGlobalSearch(query: string): Promise<OptimizedGlobalSearchResult> {
    const searchId = `global_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`
    const startTime = Date.now()

    try {
      // 精简的系统提示词
      const systemPrompt = `你是全网工具推荐专家。根据用户查询推荐优秀的在线工具。

返回JSON格式：
{
  "searchSummary": "搜索总结",
  "recommendedTools": [
    {
      "name": "工具名称",
      "url": "https://example.com",
      "description": "工具描述(50-80字)",
      "tags": ["标签1", "标签2", "标签3"],
      "category": "development",
      "subcategory": "code-editor", 
      "subsubcategory": "online-editor",
      "relevanceScore": 0.95,
      "recommendReason": "推荐理由",
      "isNewTool": true
    }
  ],
  "searchInsights": "搜索洞察"
}

分类选项：
development: code-editor, version-control, api-tools, database, deployment
design: ui-design, graphic-design, color-tools
productivity: note-taking, task-management, file-management
marketing: seo-tools, social-media, email-marketing
analytics: web-analytics, business-intelligence, survey-tools
communication: messaging, email-tools, collaboration
entertainment: media-streaming, gaming, social-platform
education: online-courses, language-learning, reference
other: utility, lifestyle, finance

要求：
1. 推荐4-8个优质工具
2. 提供真实可访问的URL
3. 使用上述分类系统
4. relevanceScore范围0.1-1.0`

      const userPrompt = `查询: ${query}

请推荐与此查询最相关的优秀在线工具。`

      console.log('快速全网搜索 - 查询:', query)

      const searchConfig = getCurrentSearchConfig()
      const aiResponse = await this.callDeepSeekAPIWithRetry(
        systemPrompt,
        userPrompt,
        searchConfig.aiService.temperature.optimized,
        searchConfig.aiService.maxTokens.optimized,
        searchConfig.globalSearch.timeout,
        searchConfig.globalSearch.maxRetries
      )
      const result = this.parseAIResponse(aiResponse)

      // 处理AI返回结果
      let finalResult: OptimizedGlobalSearchResult

      if (Array.isArray(result)) {
        finalResult = {
          searchSummary: `为您找到 ${result.length} 个优秀工具`,
          recommendedTools: result.map(tool => ({ ...tool, isNewTool: true })),
          searchInsights: `基于全网搜索，推荐了 ${result.length} 个优质工具`,
          status: 'processing',
          searchId,
          processingTime: Date.now() - startTime
        }
      } else if (result && typeof result === 'object' && result.recommendedTools) {
        finalResult = {
          searchSummary: result.searchSummary || `为您找到 ${result.recommendedTools.length} 个优秀工具`,
          recommendedTools: result.recommendedTools.map((tool: any) => ({ ...tool, isNewTool: true })) || [],
          searchInsights: result.searchInsights || '基于全网搜索的推荐结果',
          status: 'processing',
          searchId,
          processingTime: Date.now() - startTime
        }
      } else {
        finalResult = {
          searchSummary: '全网搜索完成，但未找到合适的工具',
          recommendedTools: [],
          searchInsights: '请尝试使用不同的关键词进行搜索',
          status: 'processing',
          searchId,
          processingTime: Date.now() - startTime
        }
      }

      // 应用智能默认分类到推荐的工具
      finalResult.recommendedTools = finalResult.recommendedTools.map(tool => {
        if (!tool.category || !tool.subcategory || !tool.subsubcategory) {
          const smartCategory = CategoryCorrectionService.getSmartDefaultCategory(
            tool.name || '',
            tool.description || '',
            tool.url || ''
          )

          return {
            ...tool,
            category: tool.category || smartCategory.category,
            subcategory: tool.subcategory || smartCategory.subcategory,
            subsubcategory: tool.subsubcategory || smartCategory.subsubcategory,
            relevanceScore: tool.relevanceScore || 0.7,
            recommendReason: tool.recommendReason || '全网推荐工具',
            isNewTool: true
          }
        }

        return {
          ...tool,
          relevanceScore: tool.relevanceScore || 0.7,
          recommendReason: tool.recommendReason || '全网推荐工具',
          isNewTool: true
        }
      })

      console.log('快速全网搜索完成:', finalResult)
      return finalResult

    } catch (error) {
      console.error('快速全网搜索失败:', error)

      return {
        searchSummary: '全网搜索过程中出现错误',
        recommendedTools: [],
        searchInsights: '请稍后重试或使用不同的搜索关键词',
        status: 'processing',
        searchId,
        processingTime: Date.now() - startTime
      }
    }
  }

  /**
   * 异步分类修正处理
   */
  static async processAsyncCorrection(
    searchResult: OptimizedSearchResult | OptimizedGlobalSearchResult,
    allTools?: Tool[]
  ): Promise<CategoryCorrection[]> {
    try {
      console.log(`开始异步分类修正 - 搜索ID: ${searchResult.searchId}`)

      let corrections: CategoryCorrection[] = []

      // 对于深度搜索，检查推荐的工具是否需要分类修正
      if (allTools && 'recommendedTools' in searchResult) {
        const recommendedToolIds = searchResult.recommendedTools.map(rec => rec.id)
        const toolsToCorrect = allTools.filter(tool =>
          recommendedToolIds.includes(tool.id) &&
          tool.category === 'other' &&
          tool.subcategory === 'utility' &&
          tool.subsubcategory === 'calculator'
        )

        if (toolsToCorrect.length > 0) {
          corrections = await CategoryCorrectionService.correctToolCategories(toolsToCorrect)
        }
      }

      console.log(`异步分类修正完成 - 修正了 ${corrections.length} 个工具`)
      return corrections

    } catch (error) {
      console.error('异步分类修正失败:', error)
      return []
    }
  }
}
