/**
 * 共享的网页内容获取函数
 * 避免在服务器端API之间进行HTTP调用
 */

export interface FetchContentResult {
  url: string
  title: string
  description?: string
  keywords?: string
  snippet: string
  contentType?: string
  success: boolean
  error?: string
}

export async function fetchWebContent(url: string): Promise<FetchContentResult> {
  try {
    // 验证URL格式
    new URL(url)

    // 获取页面内容
    const response = await fetch(url, {
      headers: {
        'User-Agent': 'Mozilla/5.0 (compatible; ToolMaster/1.0; +https://toolmaster.app)',
        'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8',
        'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8',
        'Accept-Encoding': 'gzip, deflate',
        'DNT': '1',
        'Connection': 'keep-alive',
        'Upgrade-Insecure-Requests': '1',
      },
      // 设置超时
      signal: AbortSignal.timeout(30000) // 30秒超时
    })

    if (!response.ok) {
      throw new Error(`HTTP ${response.status}: ${response.statusText}`)
    }

    const contentType = response.headers.get('content-type') || ''

    // 只处理HTML内容
    if (!contentType.includes('text/html')) {
      return {
        url,
        title: '',
        snippet: '',
        contentType,
        error: 'Not an HTML page',
        success: false
      }
    }

    const html = await response.text()

    // 提取标题
    const titleMatch = html.match(/<title[^>]*>([^<]+)<\/title>/i)
    const title = titleMatch ? titleMatch[1].trim() : ''

    // 提取描述
    const descriptionMatch = html.match(/<meta[^>]*name=["']description["'][^>]*content=["']([^"']+)["'][^>]*>/i) ||
                            html.match(/<meta[^>]*content=["']([^"']+)["'][^>]*name=["']description["'][^>]*>/i)
    const description = descriptionMatch ? descriptionMatch[1].trim() : ''

    // 提取关键词
    const keywordsMatch = html.match(/<meta[^>]*name=["']keywords["'][^>]*content=["']([^"']+)["'][^>]*>/i) ||
                         html.match(/<meta[^>]*content=["']([^"']+)["'][^>]*name=["']keywords["'][^>]*>/i)
    const keywords = keywordsMatch ? keywordsMatch[1].trim() : ''

    // 提取主要文本内容（移除HTML标签）
    let textContent = html
      .replace(/<script[^>]*>[\s\S]*?<\/script>/gi, '') // 移除脚本
      .replace(/<style[^>]*>[\s\S]*?<\/style>/gi, '') // 移除样式
      .replace(/<[^>]+>/g, ' ') // 移除HTML标签
      .replace(/\s+/g, ' ') // 合并空白字符
      .trim()

    // 创建内容片段
    const snippet = [
      title,
      description,
      keywords,
      textContent.substring(0, 1000)
    ].filter(Boolean).join(' ').substring(0, 2000)

    return {
      url,
      title,
      description,
      keywords,
      snippet,
      contentType,
      success: true
    }

  } catch (error) {
    console.error('Fetch content error:', error)

    // 返回基本信息，即使获取内容失败
    return {
      url,
      title: '',
      snippet: '',
      error: error instanceof Error ? error.message : 'Unknown error',
      success: false
    }
  }
}
