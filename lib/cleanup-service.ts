import { Tool } from './types'
import { ExtendedDatabase } from './database-extended'
import { AIService } from './ai-service'
import { ToolsDatabase } from './database'
import { supabase } from './supabase'

export interface CleanupResult {
  operationType: string
  originalCount: number
  processedCount: number
  removedCount: number
  details: any
  removedItems?: Tool[]
  mergedItems?: { original: Tool[], merged: Tool }[]
}

export class CleanupService {

  // 移除重复工具（基于URL匹配）
  static async removeDuplicateTools(tools: Tool[]): Promise<CleanupResult> {
    const startTime = Date.now()
    const originalCount = tools.length

    // 按URL分组
    const urlGroups = new Map<string, Tool[]>()
    const uniqueTools: Tool[] = []
    const duplicateTools: Tool[] = []

    tools.forEach(tool => {
      if (!tool.url || tool.url.trim() === '') {
        uniqueTools.push(tool)
        return
      }

      // 标准化URL（移除尾部斜杠、查询参数等）
      const normalizedUrl = this.normalizeUrl(tool.url)

      if (!urlGroups.has(normalizedUrl)) {
        urlGroups.set(normalizedUrl, [])
      }
      urlGroups.get(normalizedUrl)!.push(tool)
    })

    // 处理重复项
    for (const [url, groupTools] of urlGroups) {
      if (groupTools.length === 1) {
        uniqueTools.push(groupTools[0])
      } else {
        // 选择最完整的工具作为保留项
        const bestTool = this.selectBestTool(groupTools)
        uniqueTools.push(bestTool)

        // 其他的标记为重复
        groupTools.forEach(tool => {
          if (tool.id !== bestTool.id) {
            duplicateTools.push(tool)
          }
        })
      }
    }

    // 删除重复工具
    for (const tool of duplicateTools) {
      await ToolsDatabase.delete(tool.id)
    }

    const result: CleanupResult = {
      operationType: 'remove_duplicates',
      originalCount,
      processedCount: uniqueTools.length,
      removedCount: duplicateTools.length,
      details: {
        duplicateGroups: Array.from(urlGroups.entries())
          .filter(([, tools]) => tools.length > 1)
          .map(([url, tools]) => ({
            url,
            count: tools.length,
            kept: tools.find(t => !duplicateTools.some(d => d.id === t.id))?.name,
            removed: tools.filter(t => duplicateTools.some(d => d.id === t.id)).map(t => t.name)
          })),
        processingTimeMs: Date.now() - startTime
      },
      removedItems: duplicateTools
    }

    // 记录清理报告
    await ExtendedDatabase.createCleanupReport(
      'remove_duplicates',
      originalCount,
      uniqueTools.length,
      duplicateTools.length,
      result.details
    )

    // 记录操作日志
    await ExtendedDatabase.createOperationLog(
      '移除重复工具',
      'cleanup',
      'tool',
      undefined,
      { removedCount: duplicateTools.length, details: result.details }
    )

    return result
  }

  // 删除无效工具（URL无法访问或不完整）
  static async removeInvalidTools(tools: Tool[]): Promise<CleanupResult> {
    const startTime = Date.now()
    const originalCount = tools.length
    const invalidTools: Tool[] = []
    const validTools: Tool[] = []

    for (const tool of tools) {
      const isValid = await this.validateTool(tool)
      if (isValid) {
        validTools.push(tool)
      } else {
        invalidTools.push(tool)
      }
    }

    // 删除无效工具
    for (const tool of invalidTools) {
      await ToolsDatabase.delete(tool.id)
    }

    const result: CleanupResult = {
      operationType: 'remove_invalid',
      originalCount,
      processedCount: validTools.length,
      removedCount: invalidTools.length,
      details: {
        invalidReasons: invalidTools.map(tool => ({
          name: tool.name,
          url: tool.url,
          reason: this.getInvalidReason(tool)
        })),
        processingTimeMs: Date.now() - startTime
      },
      removedItems: invalidTools
    }

    // 记录清理报告
    await ExtendedDatabase.createCleanupReport(
      'remove_invalid',
      originalCount,
      validTools.length,
      invalidTools.length,
      result.details
    )

    // 记录操作日志
    await ExtendedDatabase.createOperationLog(
      '删除无效工具',
      'cleanup',
      'tool',
      undefined,
      { removedCount: invalidTools.length, details: result.details }
    )

    return result
  }

  // 合并重复工具的标签和描述
  static async mergeToolInfo(tools: Tool[]): Promise<CleanupResult> {
    const startTime = Date.now()
    const originalCount = tools.length

    // 按URL分组找到重复项
    const urlGroups = new Map<string, Tool[]>()

    tools.forEach(tool => {
      if (tool.url && tool.url.trim() !== '') {
        const normalizedUrl = this.normalizeUrl(tool.url)
        if (!urlGroups.has(normalizedUrl)) {
          urlGroups.set(normalizedUrl, [])
        }
        urlGroups.get(normalizedUrl)!.push(tool)
      }
    })

    const mergedItems: { original: Tool[], merged: Tool }[] = []

    for (const [url, groupTools] of urlGroups) {
      if (groupTools.length > 1) {
        try {
          // 使用AI合并工具信息
          const mergedInfo = await AIService.mergeToolInfo(groupTools)

          // 选择一个工具作为主工具，更新其信息
          const primaryTool = groupTools[0]
          const updatedTool: Tool = {
            ...primaryTool,
            name: mergedInfo.name || primaryTool.name,
            description: mergedInfo.description || primaryTool.description,
            tags: mergedInfo.tags || primaryTool.tags,
            category: mergedInfo.category || primaryTool.category,
            subcategory: mergedInfo.subcategory || primaryTool.subcategory,
            subsubcategory: mergedInfo.subsubcategory || primaryTool.subsubcategory
          }

          // 更新主工具
          // 注意：这里需要实现工具更新功能，当前数据库层可能没有

          // 删除其他重复工具
          for (let i = 1; i < groupTools.length; i++) {
            await ToolsDatabase.delete(groupTools[i].id)
          }

          mergedItems.push({
            original: groupTools,
            merged: updatedTool
          })
        } catch (error) {
          console.error('合并工具信息失败:', error)
        }
      }
    }

    // 计算正确的处理后数量
    const removedCount = mergedItems.reduce((sum, item) => sum + item.original.length - 1, 0)
    const processedCount = originalCount - removedCount

    const result: CleanupResult = {
      operationType: 'merge_tags',
      originalCount,
      processedCount,
      removedCount,
      details: {
        mergedGroups: mergedItems.map(item => ({
          originalCount: item.original.length,
          originalNames: item.original.map(t => t.name),
          mergedName: item.merged.name,
          mergedTags: item.merged.tags,
          mergedDescription: item.merged.description
        })),
        processingTimeMs: Date.now() - startTime
      },
      mergedItems
    }

    // 记录清理报告
    await ExtendedDatabase.createCleanupReport(
      'merge_tags',
      originalCount,
      processedCount,
      removedCount,
      result.details
    )

    // 记录操作日志
    await ExtendedDatabase.createOperationLog(
      '合并重复工具信息',
      'cleanup',
      'tool',
      undefined,
      { mergedCount: mergedItems.length, details: result.details }
    )

    return result
  }

  // 全面清理（组合所有清理操作）
  static async fullCleanup(tools: Tool[]): Promise<CleanupResult[]> {
    const results: CleanupResult[] = []

    // 1. 移除重复工具
    const duplicateResult = await this.removeDuplicateTools(tools)
    results.push(duplicateResult)

    // 2. 获取更新后的工具列表
    const updatedTools = await ToolsDatabase.getAll()

    // 3. 删除无效工具
    const invalidResult = await this.removeInvalidTools(updatedTools)
    results.push(invalidResult)

    // 4. 获取再次更新后的工具列表
    const finalTools = await ToolsDatabase.getAll()

    // 5. 合并工具信息
    const mergeResult = await this.mergeToolInfo(finalTools)
    results.push(mergeResult)

    // 记录综合清理日志
    const totalRemoved = results.reduce((sum, result) => sum + result.removedCount, 0)
    await ExtendedDatabase.createOperationLog(
      '全面数据清理',
      'cleanup',
      'system',
      undefined,
      {
        operations: results.length,
        totalRemoved,
        results: results.map(r => ({
          type: r.operationType,
          removed: r.removedCount
        }))
      }
    )

    return results
  }

  // 辅助方法：标准化URL
  private static normalizeUrl(url: string): string {
    try {
      const urlObj = new URL(url)
      // 移除尾部斜杠
      let pathname = urlObj.pathname.replace(/\/$/, '')
      if (pathname === '') pathname = '/'

      // 标准化域名（移除www）
      let hostname = urlObj.hostname.toLowerCase()
      if (hostname.startsWith('www.')) {
        hostname = hostname.substring(4)
      }

      return `${urlObj.protocol}//${hostname}${pathname}`
    } catch {
      return url.toLowerCase().trim()
    }
  }

  // 辅助方法：选择最佳工具
  private static selectBestTool(tools: Tool[]): Tool {
    return tools.reduce((best, current) => {
      let bestScore = this.calculateToolScore(best)
      let currentScore = this.calculateToolScore(current)

      return currentScore > bestScore ? current : best
    })
  }

  // 辅助方法：计算工具质量分数
  private static calculateToolScore(tool: Tool): number {
    let score = 0

    // 名称质量
    if (tool.name && tool.name.trim().length > 0) score += 10
    if (tool.name && tool.name.length > 5) score += 5

    // 描述质量
    if (tool.description && tool.description.trim().length > 0) score += 20
    if (tool.description && tool.description.length > 50) score += 10

    // 标签质量
    score += (tool.tags?.length || 0) * 5

    // URL质量
    if (tool.url && this.isValidUrl(tool.url)) score += 15

    // 分类完整性
    if (tool.category) score += 5
    if (tool.subcategory) score += 3
    if (tool.subsubcategory) score += 2

    // 时间因素（较新的工具稍微加分）
    const daysSinceAdded = (Date.now() - new Date(tool.addedAt).getTime()) / (1000 * 60 * 60 * 24)
    if (daysSinceAdded < 30) score += 2

    return score
  }

  // 辅助方法：验证工具有效性
  private static async validateTool(tool: Tool): Promise<boolean> {
    // 检查基本字段
    if (!tool.name || tool.name.trim().length === 0) return false
    if (!tool.url || tool.url.trim().length === 0) return false

    // 检查URL格式
    if (!this.isValidUrl(tool.url)) return false

    // 可以添加更多验证逻辑，如检查URL可访问性
    // 但为了性能考虑，这里只做基本验证

    return true
  }

  // 辅助方法：获取无效原因
  private static getInvalidReason(tool: Tool): string {
    if (!tool.name || tool.name.trim().length === 0) return '缺少工具名称'
    if (!tool.url || tool.url.trim().length === 0) return '缺少工具URL'
    if (!this.isValidUrl(tool.url)) return 'URL格式无效'
    return '未知原因'
  }

  // 辅助方法：验证URL格式
  private static isValidUrl(url: string): boolean {
    try {
      const urlObj = new URL(url)
      return urlObj.protocol === 'http:' || urlObj.protocol === 'https:'
    } catch {
      return false
    }
  }
}

// 批量导入服务
export class BatchImportService {

  // 处理文本内容导入
  static async processTextImport(
    content: string,
    taskName: string = '文本导入'
  ): Promise<string> {
    // 创建导入任务
    const task = await ExtendedDatabase.createImportTask(
      taskName,
      undefined,
      content.length,
      'text/plain',
      content.substring(0, 1000)
    )

    if (!task) {
      throw new Error('创建导入任务失败')
    }

    // 异步处理导入
    this.processImportTask(task.id, content, 'text')

    return task.id
  }

  // 处理文件导入
  static async processFileImport(
    file: File,
    taskName?: string
  ): Promise<string> {
    const content = await this.readFileContent(file)

    // 创建导入任务
    const task = await ExtendedDatabase.createImportTask(
      taskName || `文件导入: ${file.name}`,
      file.name,
      file.size,
      file.type,
      content.substring(0, 1000)
    )

    if (!task) {
      throw new Error('创建导入任务失败')
    }

    // 异步处理导入
    this.processImportTask(task.id, content, 'file')

    return task.id
  }

  // 异步处理导入任务
  private static async processImportTask(
    taskId: string,
    content: string,
    type: 'text' | 'file'
  ) {
    try {
      // 更新任务状态为处理中
      await ExtendedDatabase.updateImportTask(taskId, {
        status: 'processing',
        startedAt: new Date().toISOString()
      })

      // 检测内容类型并选择合适的处理方式
      let analyzedItems: any[] = []

      if (this.isUrlListContent(content)) {
        // URL列表处理：使用单独的URL分析
        console.log('检测到URL列表，使用单独URL分析模式')
        analyzedItems = await this.processUrlList(content, taskId)
      } else {
        // 其他内容：使用AI批量分析
        try {
          analyzedItems = await AIService.analyzeBatchContent(content)
          console.log(`AI 批量分析完成，找到 ${analyzedItems.length} 个工具`)
        } catch (error) {
          console.error('AI 分析失败，尝试备用方案:', error)

          // 备用方案：简单的 URL 提取
          const urlRegex = /https?:\/\/[^\s<>"']+/g
          const urls = content.match(urlRegex) || []

          analyzedItems = urls.slice(0, 50).map((url, index) => ({
            name: `导入工具 ${index + 1}`,
            url: url,
            description: '从文本中提取的工具',
            tags: ['导入'],
            category: 'other',
            subcategory: 'utility',
            subsubcategory: 'calculator'
          }))

          console.log(`备用方案提取了 ${analyzedItems.length} 个 URL`)
        }
      }

      // 如果没有找到任何工具，创建一个默认项
      if (analyzedItems.length === 0) {
        analyzedItems = [{
          name: '导入内容',
          url: '',
          description: '无法解析的导入内容',
          tags: ['导入', '需要手动编辑'],
          category: 'other',
          subcategory: 'utility',
          subsubcategory: 'calculator'
        }]
      }

      // 更新总数
      await ExtendedDatabase.updateImportTask(taskId, {
        totalItems: analyzedItems.length
      })

      let successCount = 0
      let failedCount = 0
      let duplicateCount = 0

      // 处理每个项目
      for (let i = 0; i < analyzedItems.length; i++) {
        const item = analyzedItems[i]
        const startTime = Date.now()

        try {
          // 检查是否重复
          const existingTools = await ToolsDatabase.getAll()
          const isDuplicate = existingTools.some(tool =>
            tool.url === item.url || tool.name === item.name
          )

          if (isDuplicate) {
            duplicateCount++
            await ExtendedDatabase.createImportTaskItem(
              taskId,
              i,
              item,
              undefined,
              undefined,
              'duplicate',
              '工具已存在'
            )
          } else {
            // 验证并修正分类信息
            const validatedCategories = await this.validateAndFixCategories(
              item.category || 'other',
              item.subcategory || 'utility',
              item.subsubcategory || 'calculator'
            )

            // 创建新工具
            const newTool = await ToolsDatabase.add({
              name: item.name || '未知工具',
              url: item.url || '',
              description: item.description || '',
              tags: item.tags || [],
              category: validatedCategories.category,
              subcategory: validatedCategories.subcategory,
              subsubcategory: validatedCategories.subsubcategory,
              sensitive: false
            })

            if (newTool) {
              successCount++
              await ExtendedDatabase.createImportTaskItem(
                taskId,
                i,
                item,
                newTool,
                newTool.id,
                'success',
                undefined,
                item,
                Date.now() - startTime
              )
            } else {
              failedCount++
              await ExtendedDatabase.createImportTaskItem(
                taskId,
                i,
                item,
                undefined,
                undefined,
                'failed',
                '创建工具失败'
              )
            }
          }
        } catch (error) {
          failedCount++
          await ExtendedDatabase.createImportTaskItem(
            taskId,
            i,
            item,
            undefined,
            undefined,
            'failed',
            error instanceof Error ? error.message : '未知错误'
          )
        }

        // 更新进度
        const progress = Math.round(((i + 1) / analyzedItems.length) * 100)
        await ExtendedDatabase.updateImportTask(taskId, {
          processedItems: i + 1,
          successItems: successCount,
          failedItems: failedCount,
          duplicateItems: duplicateCount,
          progressPercentage: progress
        })
      }

      // 完成任务
      await ExtendedDatabase.updateImportTask(taskId, {
        status: 'completed',
        completedAt: new Date().toISOString(),
        resultSummary: {
          total: analyzedItems.length,
          success: successCount,
          failed: failedCount,
          duplicate: duplicateCount
        }
      })

      // 记录操作日志
      await ExtendedDatabase.createOperationLog(
        '批量导入工具',
        'import',
        'tool',
        taskId,
        {
          type,
          total: analyzedItems.length,
          success: successCount,
          failed: failedCount,
          duplicate: duplicateCount
        }
      )

    } catch (error) {
      // 任务失败
      await ExtendedDatabase.updateImportTask(taskId, {
        status: 'failed',
        completedAt: new Date().toISOString(),
        errorDetails: {
          message: error instanceof Error ? error.message : '未知错误',
          stack: error instanceof Error ? error.stack : undefined
        }
      })

      console.error('导入任务失败:', error)
    }
  }

  // 检测内容是否主要是URL列表或书签HTML文件
  private static isUrlListContent(content: string): boolean {
    const lines = content.split('\n').filter(line => line.trim())
    if (lines.length === 0) return false

    // 检查是否为书签HTML文件
    if (this.isBookmarkHtmlContent(content)) {
      console.log('检测到书签HTML文件格式')
      return true
    }

    // 检查是否为纯文本URL列表
    const urlRegex = /^https?:\/\/[^\s<>"']+/
    const urlLines = lines.filter(line => urlRegex.test(line.trim()))
    const urlRatio = urlLines.length / lines.length

    // 如果70%以上的行是URL，则认为是URL列表
    return urlRatio >= 0.7 && urlLines.length > 0
  }

  // 检测是否为书签HTML文件
  private static isBookmarkHtmlContent(content: string): boolean {
    // 检查是否包含书签文件的特征标签
    const hasBookmarkDoctype = content.includes('<!DOCTYPE NETSCAPE-Bookmark-file-1>')
    const hasBookmarkTitle = content.includes('<TITLE>Bookmarks</TITLE>')
    const hasBookmarkLinks = /<DT><A HREF="https?:\/\/[^"]+"/i.test(content)

    // 如果包含书签文件的关键特征，则认为是书签文件
    return hasBookmarkDoctype || hasBookmarkTitle || hasBookmarkLinks
  }

  // 从书签HTML文件中提取URL
  private static extractUrlsFromBookmarkHtml(content: string): string[] {
    const urls: string[] = []

    // 使用正则表达式匹配书签链接
    const bookmarkRegex = /<DT><A HREF="(https?:\/\/[^"]+)"[^>]*>([^<]*)<\/A>/gi
    let match

    while ((match = bookmarkRegex.exec(content)) !== null) {
      const url = match[1]
      const title = match[2]

      // 验证URL格式（简单验证）
      if (url && url.startsWith('http') && url.includes('.')) {
        urls.push(url)
        console.log(`提取书签: ${title} - ${url}`)
      }
    }

    // 去重
    return [...new Set(urls)]
  }

  // 处理URL列表：对每个URL单独进行AI分析
  private static async processUrlList(content: string, taskId: string): Promise<any[]> {
    let urls: string[] = []

    // 检查是否为书签HTML文件
    if (this.isBookmarkHtmlContent(content)) {
      urls = this.extractUrlsFromBookmarkHtml(content)
      console.log(`从书签HTML文件中提取了 ${urls.length} 个URL`)
    } else {
      // 处理纯文本URL列表
      const lines = content.split('\n').filter(line => line.trim())
      const urlRegex = /^https?:\/\/[^\s<>"']+/
      urls = lines
        .filter(line => urlRegex.test(line.trim()))
        .map(line => line.trim())
    }

    // 限制最多50个URL
    urls = urls.slice(0, 50)
    console.log(`开始处理 ${urls.length} 个URL`)

    const analyzedItems: any[] = []
    let processedCount = 0

    for (const url of urls) {
      try {
        console.log(`正在分析URL ${processedCount + 1}/${urls.length}: ${url}`)

        // 调用现有的URL分析API
        const { analyzeUrl } = await import('@/lib/api-client')
        const analysis = await analyzeUrl(url)

        analyzedItems.push({
          name: analysis.name,
          url: url,
          description: analysis.description,
          tags: analysis.tags || [],
          category: analysis.category,
          subcategory: analysis.subcategory,
          subsubcategory: analysis.subsubcategory
        })

        console.log(`URL分析成功: ${analysis.name}`)
      } catch (error) {
        console.error(`URL分析失败 ${url}:`, error)

        // 单个URL分析失败时，使用基本信息
        const urlObj = new URL(url)
        const domain = urlObj.hostname.replace('www.', '')
        const name = domain.split('.')[0]

        analyzedItems.push({
          name: name.charAt(0).toUpperCase() + name.slice(1),
          url: url,
          description: `${name}是一个在线工具平台`,
          tags: ['在线工具', 'web应用'],
          category: 'other',
          subcategory: 'utility',
          subsubcategory: 'calculator'
        })
      }

      processedCount++

      // 更新进度
      const progress = Math.round((processedCount / urls.length) * 100)
      await ExtendedDatabase.updateImportTask(taskId, {
        processedItems: processedCount,
        progressPercentage: progress
      })

      // 添加小延迟避免API限制
      if (processedCount < urls.length) {
        await new Promise(resolve => setTimeout(resolve, 500))
      }
    }

    console.log(`URL列表处理完成，成功分析 ${analyzedItems.length} 个工具`)
    return analyzedItems
  }

  // 读取文件内容
  private static async readFileContent(file: File): Promise<string> {
    return new Promise((resolve, reject) => {
      const reader = new FileReader()
      reader.onload = (e) => resolve(e.target?.result as string)
      reader.onerror = (e) => reject(new Error('文件读取失败'))
      reader.readAsText(file, 'utf-8')
    })
  }

  // 获取导入任务状态
  static async getImportTaskStatus(taskId: string) {
    try {
      const { data, error } = await supabase
        .from('import_tasks')
        .select('*')
        .eq('id', taskId)
        .single()

      if (error) throw error
      return data
    } catch (error) {
      console.error('获取导入任务状态失败:', error)
      return null
    }
  }

  // 验证并修正分类信息
  private static async validateAndFixCategories(
    category: string,
    subcategory: string,
    subsubcategory: string
  ) {
    try {
      // 获取当前的分类结构
      const { data: categoriesData, error } = await supabase
        .from('categories')
        .select('*')
        .limit(1)

      if (error || !categoriesData || categoriesData.length === 0) {
        // 如果无法获取分类，使用默认值
        return {
          category: 'other',
          subcategory: 'utility',
          subsubcategory: 'calculator'
        }
      }

      const categories = categoriesData[0].data || []

      // 查找匹配的分类
      const foundCategory = categories.find((c: any) => c.id === category)
      if (!foundCategory) {
        return {
          category: 'other',
          subcategory: 'utility',
          subsubcategory: 'calculator'
        }
      }

      // 查找匹配的子分类
      const foundSubcategory = foundCategory.subcategories?.find((s: any) => s.id === subcategory)
      if (!foundSubcategory) {
        // 使用第一个可用的子分类
        const firstSubcategory = foundCategory.subcategories?.[0]
        if (firstSubcategory) {
          const firstSubsubcategory = firstSubcategory.subsubcategories?.[0]
          return {
            category: foundCategory.id,
            subcategory: firstSubcategory.id,
            subsubcategory: firstSubsubcategory?.id || 'calculator'
          }
        }
        return {
          category: 'other',
          subcategory: 'utility',
          subsubcategory: 'calculator'
        }
      }

      // 查找匹配的子子分类
      const foundSubsubcategory = foundSubcategory.subsubcategories?.find((ss: any) => ss.id === subsubcategory)
      if (!foundSubsubcategory) {
        // 使用第一个可用的子子分类
        const firstSubsubcategory = foundSubcategory.subsubcategories?.[0]
        return {
          category: foundCategory.id,
          subcategory: foundSubcategory.id,
          subsubcategory: firstSubsubcategory?.id || 'calculator'
        }
      }

      // 所有分类都匹配
      return {
        category: foundCategory.id,
        subcategory: foundSubcategory.id,
        subsubcategory: foundSubsubcategory.id
      }

    } catch (error) {
      console.error('验证分类失败:', error)
      return {
        category: 'other',
        subcategory: 'utility',
        subsubcategory: 'calculator'
      }
    }
  }
}
