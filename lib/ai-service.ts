import { ExtendedDatabase } from './database-extended'
import { Tool } from './types'
import { AIUsageMonitor } from './ai-usage-monitor'

// AI 分析服务类
export class AIService {
  private static getApiUrl(): string {
    return process.env.DEEPSEEK_API_URL || 'https://api.deepseek.com/chat/completions'
  }

  private static getApiKey(): string {
    const apiKey = process.env.DEEPSEEK_API_KEY
    if (!apiKey) {
      throw new Error('DEEPSEEK_API_KEY environment variable is required')
    }
    return apiKey
  }

  // 验证分类是否有效的函数
  private static validateCategory(category: string, subcategory: string, subsubcategory: string): boolean {
    const validCategories = {
      'development': {
        'code-editor': ['online-editor', 'ide', 'text-editor', 'code-formatter'],
        'version-control': ['git-tools', 'collaboration', 'code-review', 'repository'],
        'api-tools': ['api-testing', 'api-docs', 'mock-tools', 'api-gateway'],
        'database': ['db-client', 'db-design', 'db-migration', 'db-backup'],
        'deployment': ['ci-cd', 'monitoring', 'container', 'cloud-platform'],
        'debugging': ['debugger', 'unit-testing', 'integration-testing', 'performance-testing']
      },
      'design': {
        'ui-design': ['prototyping', 'wireframe', 'design-system', 'user-testing'],
        'graphics': ['vector-graphics', 'photo-editing', 'illustration', 'logo-design'],
        'color-tools': ['color-picker', 'color-palette', 'gradient', 'color-theory'],
        'icon-fonts': ['icon-library', 'font-tools', 'emoji', 'typography'],
        '3d-design': ['3d-modeling', '3d-rendering', '3d-animation', '3d-printing'],
        'multimedia': ['video-editing', 'audio-editing', 'motion-graphics', 'presentation']
      },
      'productivity': {
        'note-taking': ['markdown', 'knowledge-base', 'mind-map', 'note-sync'],
        'task-management': ['todo-list', 'project-management', 'time-tracking', 'team-collaboration'],
        'automation': ['workflow', 'scripting', 'integration', 'scheduling'],
        'office-tools': ['document', 'spreadsheet', 'presentation', 'pdf-tools'],
        'time-management': ['pomodoro', 'time-tracking', 'scheduling', 'efficiency-analysis'],
        'communication': ['instant-messaging', 'video-conferencing', 'file-sharing', 'collaborative-whiteboard']
      },
      'learning': {
        'programming': ['coding-practice', 'algorithm', 'tutorial', 'code-challenge'],
        'language': ['vocabulary', 'grammar', 'pronunciation', 'conversation'],
        'reference': ['documentation', 'cheatsheet', 'examples', 'wiki'],
        'online-courses': ['mooc', 'video-courses', 'certification', 'live-courses'],
        'skill-training': ['professional-skills', 'soft-skills', 'hands-on-training', 'skill-assessment'],
        'academic-research': ['paper-writing', 'literature-management', 'data-collection', 'research-tools']
      },
      'entertainment': {
        'media-streaming': ['video-streaming', 'music-streaming', 'podcast', 'live-streaming'],
        'games': ['online-games', 'game-tools', 'game-community', 'mobile-games'],
        'content-download': ['video-download', 'music-download', 'movie-download', 'ebook-download'],
        'social-entertainment': ['social-media', 'chat-tools', 'community', 'dating-apps'],
        'creative-entertainment': ['creation-platform', 'art-creation', 'music-creation', 'writing-creation'],
        'interactive-entertainment': ['vr-experience', 'ar-application', 'motion-games', 'interactive-fun']
      },
      'life-service': {
        'daily-tools': ['weather', 'calendar', 'reminder', 'alarm-clock'],
        'travel': ['map-navigation', 'booking', 'travel-guide', 'transportation'],
        'health-fitness': ['fitness-tracker', 'health-monitor', 'nutrition', 'medical-info'],
        'finance': ['budget-tracker', 'investment', 'currency', 'banking'],
        'shopping': ['price-comparison', 'coupons', 'shopping-assistant', 'expense-tracking'],
        'home-living': ['smart-home', 'household-management', 'home-decoration', 'life-tips']
      },
      'business': {
        'marketing': ['seo-tools', 'social-marketing', 'email-marketing', 'content-marketing'],
        'analytics': ['web-analytics', 'business-intelligence', 'data-visualization', 'market-research'],
        'customer-service': ['live-chat', 'help-desk', 'feedback', 'crm-system'],
        'e-commerce': ['online-store', 'payment', 'inventory', 'logistics'],
        'human-resources': ['recruitment', 'employee-management', 'training-development', 'performance-evaluation'],
        'financial-management': ['accounting', 'invoice-management', 'cost-control', 'financial-reports']
      },
      'system': {
        'network-tools': ['speed-test', 'dns-tools', 'proxy-vpn', 'network-monitor'],
        'security': ['password-manager', 'encryption', 'security-scan', 'antivirus'],
        'file-tools': ['file-converter', 'file-compress', 'file-recovery', 'file-sync'],
        'system-monitor': ['performance', 'resource-usage', 'system-info', 'disk-cleanup'],
        'system-optimization': ['startup-optimization', 'memory-cleanup', 'registry-cleanup', 'system-acceleration'],
        'hardware-tools': ['hardware-detection', 'driver-management', 'temperature-monitoring', 'hardware-testing']
      },
      'ai-tools': {
        'text-generation': ['chatbot', 'writing-assistant', 'content-creation', 'translation'],
        'image-generation': ['ai-art', 'photo-enhancement', 'image-editing', 'avatar-generator'],
        'code-assistant': ['code-completion', 'code-review', 'bug-detection', 'code-generation'],
        'data-analysis': ['data-mining', 'predictive-analysis', 'pattern-recognition', 'ai-research'],
        'voice-processing': ['speech-recognition', 'text-to-speech', 'voice-translation', 'voice-analysis'],
        'intelligent-decision': ['decision-support', 'risk-assessment', 'intelligent-recommendation', 'strategy-optimization']
      },
      'other': {
        'utility': ['calculator', 'unit-converter', 'qr-generator', 'random-generator'],
        'generator': ['text-generator', 'image-generator', 'data-generator', 'password-generator'],
        'testing': ['website-test', 'performance-test', 'compatibility-test', 'load-test'],
        'converter': ['format-converter', 'encoding-converter', 'time-converter', 'currency-converter'],
        'development-helper': ['code-snippets', 'dev-documentation', 'dev-environment', 'dev-resources'],
        'fun-tools': ['prank-tools', 'fun-tests', 'creative-generator', 'mini-games']
      }
    }

    return validCategories[category]?.[subcategory]?.includes(subsubcategory) || false
  }

  // 生成URL的SHA256哈希（用于缓存）
  private static async generateUrlHash(url: string): Promise<string> {
    const encoder = new TextEncoder()
    const data = encoder.encode(url)
    const hashBuffer = await crypto.subtle.digest('SHA-256', data)
    const hashArray = Array.from(new Uint8Array(hashBuffer))
    return hashArray.map(b => b.toString(16).padStart(2, '0')).join('')
  }

  // 调用 DeepSeek API（带监控）
  private static async callDeepSeekAPI(
    systemPrompt: string,
    userPrompt: string,
    temperature: number = 0.3,
    maxTokens: number = 1000,
    apiType: 'deep-search' | 'global-search' | 'analyze-url' | 'add-tool' = 'analyze-url',
    requestId?: string
  ): Promise<string> {
    const startTime = Date.now()
    let success = false
    let errorMessage: string | undefined
    let inputTokens = 0
    let outputTokens = 0

    try {
      const response = await fetch(this.getApiUrl(), {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${this.getApiKey()}`
        },
        body: JSON.stringify({
          model: 'deepseek-chat',
          messages: [
            { role: 'system', content: systemPrompt },
            { role: 'user', content: userPrompt }
          ],
          temperature,
          max_tokens: maxTokens,
          stream: false
        })
      })

      if (!response.ok) {
        throw new Error(`DeepSeek API error: ${response.status} ${response.statusText}`)
      }

      const result = await response.json()
      const content = result.choices[0]?.message?.content || ''

      // 获取 token 使用情况
      if (result.usage) {
        inputTokens = result.usage.prompt_tokens || 0
        outputTokens = result.usage.completion_tokens || 0
      } else {
        // 如果 API 没有返回 usage 信息，进行估算
        inputTokens = Math.ceil((systemPrompt.length + userPrompt.length) / 4) // 粗略估算
        outputTokens = Math.ceil(content.length / 4)
      }

      success = true
      return content
    } catch (error) {
      success = false
      errorMessage = error instanceof Error ? error.message : String(error)
      console.error('DeepSeek API 调用失败:', error)
      throw error
    } finally {
      // 记录使用情况（异步，不影响主流程）
      const responseTime = Date.now() - startTime
      AIUsageMonitor.logUsage({
        apiType,
        model: 'deepseek-chat',
        inputTokens,
        outputTokens,
        responseTimeMs: responseTime,
        success,
        errorMessage,
        requestId,
        userQuery: userPrompt.substring(0, 200) // 只记录前200字符，保护隐私
      }).catch(err => {
        console.error('记录 AI 使用情况失败:', err)
      })
    }
  }

  // 解析 AI 返回的 JSON
  private static parseAIResponse(text: string): any {
    try {
      // 移除 markdown 代码块
      let jsonText = text.trim()

      if (jsonText.includes('```json')) {
        const jsonMatch = jsonText.match(/```json\s*([\s\S]*?)\s*```/)
        if (jsonMatch) {
          jsonText = jsonMatch[1].trim()
        }
      } else if (jsonText.includes('```')) {
        const jsonMatch = jsonText.match(/```\s*([\s\S]*?)\s*```/)
        if (jsonMatch) {
          jsonText = jsonMatch[1].trim()
        }
      }

      // 查找 JSON 对象或数组
      let jsonMatch = jsonText.match(/\[[\s\S]*\]/) || jsonText.match(/\{[\s\S]*\}/)
      if (jsonMatch) {
        jsonText = jsonMatch[0]
      }

      // 清理可能的格式问题
      jsonText = jsonText
        .replace(/,\s*}/g, '}')  // 移除对象末尾多余的逗号
        .replace(/,\s*]/g, ']')  // 移除数组末尾多余的逗号
        .replace(/[\u201C\u201D]/g, '"')  // 替换中文引号
        .replace(/[\u2018\u2019]/g, "'")  // 替换中文单引号

      return JSON.parse(jsonText)
    } catch (error) {
      console.error('解析 AI 响应失败:', error)
      console.error('原始响应:', text)

      // 尝试手动解析或返回默认值
      return this.fallbackParse(text)
    }
  }

  // 备用解析方法
  private static fallbackParse(text: string): any {
    try {
      // 尝试提取基本信息
      const nameMatch = text.match(/["']?name["']?\s*:\s*["']([^"']+)["']/i)
      const urlMatch = text.match(/["']?url["']?\s*:\s*["']([^"']+)["']/i)
      const descMatch = text.match(/["']?description["']?\s*:\s*["']([^"']+)["']/i)

      return [{
        name: nameMatch ? nameMatch[1] : '未知工具',
        url: urlMatch ? urlMatch[1] : '',
        description: descMatch ? descMatch[1] : '导入的工具',
        tags: ['导入'],
        category: 'other',
        subcategory: 'utility',
        subsubcategory: 'calculator'
      }]
    } catch (error) {
      console.error('备用解析也失败:', error)
      return []
    }
  }

  // 单个 URL 分析
  static async analyzeUrl(url: string, useCache: boolean = true): Promise<any> {
    const startTime = Date.now()

    try {
      // 检查缓存
      if (useCache) {
        const urlHash = await this.generateUrlHash(url)
        const cached = await ExtendedDatabase.getAIAnalysisCache(urlHash)
        if (cached) {
          console.log('使用缓存的 AI 分析结果:', url)
          return cached.analysis_result
        }
      }

      // 获取页面内容
      let snippet = ''
      try {
        // 注意：这里是在服务端调用，需要使用完整URL
        // 在实际部署时，这里应该使用内部API调用或直接调用函数
        console.warn('AI服务中的fetch-content调用需要在客户端处理')
      } catch (error) {
        console.warn('获取页面内容失败，使用 URL 分析:', error)
      }

      const systemPrompt = `你是一个专业的网站分析和工具分类专家。请根据用户提供的网站URL和页面内容，准确分析并生成以下信息：

1. 工具名称：提取网站的真实名称（如：腾讯视频、GitHub、Figma等）
2. 工具描述：生成50-100字的准确描述，说明工具的主要功能和特点
3. 相关标签：生成3-6个相关的中文标签
4. 分类信息：将工具分类到最合适的三级目录结构中

## 完整分类结构：

**development (开发工具)**
- code-editor (代码编辑): online-editor, ide, text-editor, code-formatter
- version-control (版本控制): git-tools, collaboration, code-review, repository
- api-tools (API工具): api-testing, api-docs, mock-tools, api-gateway
- database (数据库工具): db-client, db-design, db-migration, db-backup
- deployment (部署运维): ci-cd, monitoring, container, cloud-platform
- debugging (调试测试): debugger, unit-testing, integration-testing, performance-testing

**design (设计工具)**
- ui-design (UI设计): prototyping, wireframe, design-system, user-testing
- graphics (图形设计): vector-graphics, photo-editing, illustration, logo-design
- color-tools (色彩工具): color-picker, color-palette, gradient, color-theory
- icon-fonts (图标字体): icon-library, font-tools, emoji, typography
- 3d-design (3D设计): 3d-modeling, 3d-rendering, 3d-animation, 3d-printing
- multimedia (多媒体设计): video-editing, audio-editing, motion-graphics, presentation

**productivity (效率工具)**
- note-taking (笔记工具): markdown, knowledge-base, mind-map, note-sync
- task-management (任务管理): todo-list, project-management, time-tracking, team-collaboration
- automation (自动化工具): workflow, scripting, integration, scheduling
- office-tools (办公工具): document, spreadsheet, presentation, pdf-tools

**learning (学习资源)**
- programming (编程学习): coding-practice, algorithm, tutorial, code-challenge
- language (语言学习): vocabulary, grammar, pronunciation, conversation
- reference (参考资料): documentation, cheatsheet, examples, wiki
- online-courses (在线课程): mooc, video-courses, certification, live-courses

**entertainment (娱乐工具)**
- media-streaming (媒体播放): video-streaming, music-streaming, podcast, live-streaming
- games (游戏娱乐): online-games, game-tools, game-community, mobile-games
- content-download (内容下载): video-download, music-download, movie-download, ebook-download
- social-entertainment (社交娱乐): social-media, chat-tools, community, dating-apps

**life-service (生活服务)**
- daily-tools (日常工具): weather, calendar, reminder, alarm-clock
- travel (旅行出行): map-navigation, booking, travel-guide, transportation
- health-fitness (健康健身): fitness-tracker, health-monitor, nutrition, medical-info
- finance (金融理财): budget-tracker, investment, currency, banking

**business (商业工具)**
- marketing (营销推广): seo-tools, social-marketing, email-marketing, content-marketing
- analytics (数据分析): web-analytics, business-intelligence, data-visualization, market-research
- customer-service (客户服务): live-chat, help-desk, feedback, crm-system
- e-commerce (电子商务): online-store, payment, inventory, logistics

**system (系统工具)**
- network-tools (网络工具): speed-test, dns-tools, proxy-vpn, network-monitor
- security (安全工具): password-manager, encryption, security-scan, antivirus
- file-tools (文件工具): file-converter, file-compress, file-recovery, file-sync
- system-monitor (系统监控): performance, resource-usage, system-info, disk-cleanup

**ai-tools (AI工具)**
- text-generation (文本生成): chatbot, writing-assistant, content-creation, translation
- image-generation (图像生成): ai-art, photo-enhancement, image-editing, avatar-generator
- code-assistant (代码助手): code-completion, code-review, bug-detection, code-generation
- data-analysis (数据分析): data-mining, predictive-analysis, pattern-recognition, ai-research

**other (其他工具)**
- utility (实用工具): calculator, unit-converter, qr-generator, random-generator
- generator (生成工具): text-generator, image-generator, data-generator, password-generator
- testing (测试工具): website-test, performance-test, compatibility-test, load-test
- converter (转换工具): format-converter, encoding-converter, time-converter, currency-converter

## 分析要求：
1. 仔细分析URL和页面内容，识别网站的真实身份
2. 生成准确的中文名称和描述
3. 选择最合适的三级分类
4. 如果现有分类不合适，可以建议新的二级或三级分类
5. 必须返回标准的JSON格式

请返回JSON格式的结果，包含以下字段：
{
  "name": "工具的真实名称",
  "description": "50-100字的准确描述",
  "tags": ["标签1", "标签2", "标签3"],
  "category": "一级分类ID",
  "subcategory": "二级分类ID",
  "subsubcategory": "三级分类ID",
  "confidence": 0.95
}`

      const userPrompt = `请分析以下网站：
URL: ${url}
页面内容片段: ${snippet.substring(0, 2000)}`

      const aiResponse = await this.callDeepSeekAPI(systemPrompt, userPrompt, 0.3, 1000, 'analyze-url', `analyze-${url}`)
      const analysisResult = this.parseAIResponse(aiResponse)

      const processingTime = Date.now() - startTime

      // 缓存结果
      if (useCache) {
        const urlHash = await this.generateUrlHash(url)
        await ExtendedDatabase.setAIAnalysisCache(
          urlHash,
          url,
          analysisResult,
          analysisResult.confidence || 0.8,
          processingTime
        )
      }

      return analysisResult
    } catch (error) {
      console.error('URL 分析失败:', error)
      throw error
    }
  }

  // 批量分析文本内容
  static async analyzeBatchContent(content: string): Promise<any[]> {
    try {
      // 首先尝试直接解析 JSON 内容（如果是导出的 JSON 文件）
      const directParseResult = this.tryDirectJsonParse(content)
      if (directParseResult.length > 0) {
        console.log('直接解析 JSON 成功，找到', directParseResult.length, '个工具')
        return directParseResult
      }

      // 如果不是 JSON，则使用 AI 分析
      const systemPrompt = `你是一个专业的工具信息提取专家。请从用户提供的文本内容中提取所有可能的工具信息。

文本内容可能包含：
1. 网站URL列表
2. 工具名称列表
3. 书签导出文件内容
4. 混合格式的工具信息

请提取每个工具的以下信息：
- name: 工具名称
- url: 工具链接（如果没有URL但有名称，请尝试推测常见的官方网站）
- description: 工具描述（如果没有描述，请根据名称生成简短描述）
- tags: 相关标签数组
- category: 一级分类
- subcategory: 二级分类
- subsubcategory: 三级分类

## 完整分类结构：

**development (开发工具)**
- code-editor (代码编辑): online-editor, ide, text-editor, code-formatter
- version-control (版本控制): git-tools, collaboration, code-review, repository
- api-tools (API工具): api-testing, api-docs, mock-tools, api-gateway
- database (数据库工具): db-client, db-design, db-migration, db-backup
- deployment (部署运维): ci-cd, monitoring, container, cloud-platform
- debugging (调试测试): debugger, unit-testing, integration-testing, performance-testing

**design (设计工具)**
- ui-design (UI设计): prototyping, wireframe, design-system, user-testing
- graphics (图形设计): vector-graphics, photo-editing, illustration, logo-design
- color-tools (色彩工具): color-picker, color-palette, gradient, color-theory
- icon-fonts (图标字体): icon-library, font-tools, emoji, typography
- 3d-design (3D设计): 3d-modeling, 3d-rendering, 3d-animation, 3d-printing
- multimedia (多媒体设计): video-editing, audio-editing, motion-graphics, presentation

**productivity (效率工具)**
- note-taking (笔记工具): markdown, knowledge-base, mind-map, note-sync
- task-management (任务管理): todo-list, project-management, time-tracking, team-collaboration
- automation (自动化工具): workflow, scripting, integration, scheduling
- office-tools (办公工具): document, spreadsheet, presentation, pdf-tools

**learning (学习资源)**
- programming (编程学习): coding-practice, algorithm, tutorial, code-challenge
- language (语言学习): vocabulary, grammar, pronunciation, conversation
- reference (参考资料): documentation, cheatsheet, examples, wiki
- online-courses (在线课程): mooc, video-courses, certification, live-courses

**entertainment (娱乐工具)**
- media-streaming (媒体播放): video-streaming, music-streaming, podcast, live-streaming
- games (游戏娱乐): online-games, game-tools, game-community, mobile-games
- content-download (内容下载): video-download, music-download, movie-download, ebook-download
- social-entertainment (社交娱乐): social-media, chat-tools, community, dating-apps

**life-service (生活服务)**
- daily-tools (日常工具): weather, calendar, reminder, alarm-clock
- travel (旅行出行): map-navigation, booking, travel-guide, transportation
- health-fitness (健康健身): fitness-tracker, health-monitor, nutrition, medical-info
- finance (金融理财): budget-tracker, investment, currency, banking

**business (商业工具)**
- marketing (营销推广): seo-tools, social-marketing, email-marketing, content-marketing
- analytics (数据分析): web-analytics, business-intelligence, data-visualization, market-research
- customer-service (客户服务): live-chat, help-desk, feedback, crm-system
- e-commerce (电子商务): online-store, payment, inventory, logistics

**system (系统工具)**
- network-tools (网络工具): speed-test, dns-tools, proxy-vpn, network-monitor
- security (安全工具): password-manager, encryption, security-scan, antivirus
- file-tools (文件工具): file-converter, file-compress, file-recovery, file-sync
- system-monitor (系统监控): performance, resource-usage, system-info, disk-cleanup

**ai-tools (AI工具)**
- text-generation (文本生成): chatbot, writing-assistant, content-creation, translation
- image-generation (图像生成): ai-art, photo-enhancement, image-editing, avatar-generator
- code-assistant (代码助手): code-completion, code-review, bug-detection, code-generation
- data-analysis (数据分析): data-mining, predictive-analysis, pattern-recognition, ai-research

**other (其他工具)**
- utility (实用工具): calculator, unit-converter, qr-generator, random-generator
- generator (生成工具): text-generator, image-generator, data-generator, password-generator
- testing (测试工具): website-test, performance-test, compatibility-test, load-test
- converter (转换工具): format-converter, encoding-converter, time-converter, currency-converter

请返回JSON数组格式：
[
  {
    "name": "工具名称",
    "url": "https://example.com",
    "description": "工具描述",
    "tags": ["标签1", "标签2"],
    "category": "分类",
    "subcategory": "子分类",
    "subsubcategory": "子子分类"
  }
]

重要：请根据工具的实际功能选择最合适的分类，不要全部归类到 other > utility > calculator。如果某个字段无法确定，请使用合理的默认值。`

      const userPrompt = `请从以下内容中提取工具信息：

${content.substring(0, 8000)}`

      const aiResponse = await this.callDeepSeekAPI(systemPrompt, userPrompt, 0.3, 2000, 'add-tool', `extract-${Date.now()}`)
      const result = this.parseAIResponse(aiResponse)

      // 确保返回数组
      const finalResult = Array.isArray(result) ? result : [result]
      return finalResult.filter(item => item && (item.name || item.url))
    } catch (error) {
      console.error('批量内容分析失败:', error)

      // 尝试简单的 URL 提取作为备用方案
      return this.extractUrlsAsFallback(content)
    }
  }

  // 尝试直接解析 JSON 内容
  private static tryDirectJsonParse(content: string): any[] {
    try {
      const trimmed = content.trim()

      // 检查是否是 ToolMaster 导出的 JSON 格式
      if (trimmed.startsWith('{') && trimmed.includes('"tools"')) {
        const parsed = JSON.parse(trimmed)
        if (parsed.tools && Array.isArray(parsed.tools)) {
          return parsed.tools.map((tool: any) => ({
            name: tool.name || '未知工具',
            url: tool.url || '',
            description: tool.description || '',
            tags: Array.isArray(tool.tags) ? tool.tags : [],
            category: tool.category || 'other',
            subcategory: tool.subcategory || 'utility',
            subsubcategory: tool.subsubcategory || 'calculator'
          }))
        }
      }

      // 检查是否是工具数组
      if (trimmed.startsWith('[')) {
        const parsed = JSON.parse(trimmed)
        if (Array.isArray(parsed)) {
          return parsed.map((tool: any) => ({
            name: tool.name || '未知工具',
            url: tool.url || '',
            description: tool.description || '',
            tags: Array.isArray(tool.tags) ? tool.tags : [],
            category: tool.category || 'other',
            subcategory: tool.subcategory || 'utility',
            subsubcategory: tool.subsubcategory || 'calculator'
          }))
        }
      }
    } catch (error) {
      // 不是有效的 JSON，继续其他处理
    }

    return []
  }

  // 备用方案：提取 URL
  private static extractUrlsAsFallback(content: string): any[] {
    const urlRegex = /https?:\/\/[^\s<>"']+/g
    const urls = content.match(urlRegex) || []

    return urls.slice(0, 20).map(url => ({
      name: this.extractNameFromUrl(url),
      url: url,
      description: '从文本中提取的工具',
      tags: ['导入'],
      category: 'other',
      subcategory: 'utility',
      subsubcategory: 'calculator'
    }))
  }

  // 从 URL 提取名称
  private static extractNameFromUrl(url: string): string {
    try {
      const domain = new URL(url).hostname.replace('www.', '')
      const parts = domain.split('.')
      if (parts.length >= 2) {
        const name = parts[0]
        return name.charAt(0).toUpperCase() + name.slice(1)
      }
      return domain
    } catch {
      return '未知工具'
    }
  }

  // 工具信息补全（根据名称推测URL和其他信息）
  static async completeToolInfo(toolName: string): Promise<any> {
    const systemPrompt = `你是一个工具信息补全专家。根据用户提供的工具名称，请补全以下信息：

1. 推测官方网站URL
2. 生成准确的工具描述
3. 生成相关标签
4. 进行分类

请返回JSON格式：
{
  "name": "标准化的工具名称",
  "url": "推测的官方网站URL",
  "description": "工具描述",
  "tags": ["标签1", "标签2"],
  "category": "分类",
  "subcategory": "子分类",
  "subsubcategory": "子子分类",
  "confidence": 0.8
}

如果无法推测URL，请返回空字符串。`

    const userPrompt = `工具名称: ${toolName}`

    try {
      const aiResponse = await this.callDeepSeekAPI(systemPrompt, userPrompt, 0.3, 1000, 'add-tool', `complete-${toolName}`)
      return this.parseAIResponse(aiResponse)
    } catch (error) {
      console.error('工具信息补全失败:', error)
      throw error
    }
  }

  // 数据清理：合并重复工具的标签和描述
  static async mergeToolInfo(tools: Tool[]): Promise<any> {
    const systemPrompt = `你是一个数据清理专家。请分析提供的重复工具列表，将它们的标签和描述信息进行智能合并。

合并规则：
1. 保留最完整和准确的描述
2. 合并所有有用的标签，去除重复
3. 选择最准确的分类
4. 保留最新的添加时间

请返回JSON格式：
{
  "name": "最终工具名称",
  "url": "最终URL",
  "description": "合并后的描述",
  "tags": ["合并后的标签"],
  "category": "最终分类",
  "subcategory": "最终子分类",
  "subsubcategory": "最终子子分类",
  "mergeReason": "合并原因说明"
}`

    const userPrompt = `请合并以下重复工具的信息：
${JSON.stringify(tools, null, 2)}`

    try {
      const aiResponse = await this.callDeepSeekAPI(systemPrompt, userPrompt, 0.3, 1000, 'add-tool', `merge-${Date.now()}`)
      return this.parseAIResponse(aiResponse)
    } catch (error) {
      console.error('工具信息合并失败:', error)
      throw error
    }
  }

  // 深度搜索：基于用户查询在本地工具库中进行智能搜索和推荐
  static async deepSearch(query: string, allTools: Tool[]): Promise<any> {
    try {
      // 构建工具数据摘要，减少token消耗
      const toolsSummary = allTools.map(tool => ({
        id: tool.id,
        name: tool.name,
        url: tool.url,
        description: tool.description,
        tags: tool.tags,
        category: tool.category,
        subcategory: tool.subcategory,
        subsubcategory: tool.subsubcategory
      }))

      const systemPrompt = `你是一个专业的工具推荐专家。你的任务是根据用户的查询需求，从提供的本地工具库中寻找和推荐相关的工具。

## 核心要求：
1. **优先匹配原则**：只要工具库中有任何与用户查询相关的工具，都应该推荐出来
2. **宽松匹配策略**：不要过于严格，要从名称、描述、标签、分类等多个维度寻找相关性
3. **本地优先**：只能推荐工具库中实际存在的工具，不能推荐不存在的工具
4. **相关性判断**：即使不是完全匹配，只要有一定相关性就应该推荐

## 匹配策略：
- **直接匹配**：工具名称、描述中包含查询关键词
- **功能匹配**：工具功能与用户需求相关
- **分类匹配**：工具所属分类与查询意图相关
- **标签匹配**：工具标签与查询内容相关
- **语义匹配**：理解查询的深层含义，寻找功能相似的工具

## 示例说明：
- 用户查询"社区论坛"时，应该推荐知乎、豆瓣、天涯社区等社交平台
- 用户查询"好玩的社区"时，应该推荐所有社交、社区类工具
- 用户查询"有名的论坛"时，应该推荐知名的讨论平台和社区网站

## 响应格式：
请严格按照以下JSON格式返回，不要添加任何其他内容：
{
  "searchSummary": "对用户查询的理解总结（20-30字）",
  "recommendedTools": [
    {
      "id": "工具ID（必须是工具库中实际存在的ID）",
      "relevanceScore": 0.85,
      "recommendReason": "推荐理由（30-50字，说明为什么推荐这个工具）"
    }
  ],
  "searchInsights": "搜索洞察（50-80字，对整体搜索结果的专业分析）"
}

## 重要要求：
1. **必须推荐**：如果工具库中有任何相关工具，都必须推荐出来，不能返回空结果
2. **ID准确性**：推荐的工具ID必须是工具库中实际存在的ID
3. **宽松匹配**：不要过于严格，只要有一定相关性就应该推荐
4. **相关性评分**：即使相关性不高，也要给出合理的评分（0.3-1.0之间）
5. **推荐理由**：基于工具的实际功能特点给出推荐理由
6. **优先本地**：优先推荐本地工具库中的相关工具，而不是说没有合适的工具`

      const userPrompt = `用户查询：${query}

本地工具库数据（共${toolsSummary.length}个工具）：
${JSON.stringify(toolsSummary, null, 2)}

请仔细分析以上工具库中的每个工具，从名称、描述、标签、分类等维度寻找与用户查询相关的工具。

**重要提醒**：
1. 如果用户查询"社区论坛"、"好玩的社区"、"有名的论坛"等，应该推荐知乎、豆瓣、天涯社区、百度贴吧等社交平台
2. 即使相关性不是100%匹配，只要有一定关联性就应该推荐
3. 不要返回空的recommendedTools数组，必须从工具库中找出相关工具
4. 宁可推荐相关性较低的工具，也不要说没有合适的工具`

      // 添加调试信息
      console.log('深度搜索调试信息:')
      console.log('查询:', query)
      console.log('工具数量:', toolsSummary.length)
      console.log('工具示例:', toolsSummary.slice(0, 3))

      const aiResponse = await this.callDeepSeekAPI(systemPrompt, userPrompt, 0.7, 2000, 'deep-search', `search-${query.substring(0, 20)}`)
      const result = this.parseAIResponse(aiResponse)

      console.log('AI原始返回:', result)

      // 处理AI可能返回的不同格式
      let finalResult

      if (Array.isArray(result)) {
        // 如果AI直接返回了推荐工具数组
        console.log('AI返回了数组格式，转换为对象格式')
        finalResult = {
          searchSummary: `为您的查询找到了 ${result.length} 个相关工具`,
          recommendedTools: result,
          searchInsights: `基于您的搜索需求，我们推荐了 ${result.length} 个最相关的工具，按相关性排序`
        }
      } else if (result && typeof result === 'object') {
        // 如果AI返回了完整的对象格式
        finalResult = result
      } else {
        throw new Error('AI返回的结果格式无效')
      }

      // 确保必要字段存在
      if (!finalResult.searchSummary) {
        finalResult.searchSummary = '搜索分析'
      }
      if (!finalResult.searchInsights) {
        finalResult.searchInsights = '暂无分析洞察'
      }
      if (!finalResult.recommendedTools || !Array.isArray(finalResult.recommendedTools)) {
        finalResult.recommendedTools = []
      }

      // 验证推荐的工具ID是否存在，并确保数据结构正确
      finalResult.recommendedTools = finalResult.recommendedTools.filter(rec => {
        if (!rec || typeof rec !== 'object') return false
        if (!rec.id || typeof rec.relevanceScore !== 'number') return false
        return allTools.some(tool => tool.id === rec.id)
      })

      // 确保推荐理由存在
      finalResult.recommendedTools = finalResult.recommendedTools.map(rec => ({
        ...rec,
        recommendReason: rec.recommendReason || '推荐理由暂无'
      }))

      console.log('最终处理结果:', finalResult)
      return finalResult
    } catch (error) {
      console.error('深度搜索失败:', error)
      throw error
    }
  }

  // 全网搜索：基于用户查询在全网范围内搜索和推荐最佳工具
  static async globalSearch(query: string): Promise<any> {
    try {
      const systemPrompt = `你是一个专业的全网工具推荐专家，拥有丰富的互联网工具使用经验和全面的市场洞察力。你的任务是根据用户的查询需求，在全网范围内搜索和推荐最优秀的工具。

## 核心要求：
1. **全网视野**：不局限于特定平台，从全网范围内寻找最优秀的工具
2. **权威推荐**：基于工具的实际功能、用户评价、市场地位进行专业评估
3. **实用导向**：优先推荐真实可用、功能强大、用户体验好的工具
4. **多样化选择**：提供不同类型和价位的工具选择

## 评估标准：
- **功能完整性**：工具功能是否完整、强大
- **用户体验**：界面友好度、易用性
- **稳定可靠**：服务稳定性、更新频率
- **市场认可度**：用户数量、行业评价
- **性价比**：免费/付费工具的价值比

## 完整分类体系（必须使用三级分类）：
**重要：所有工具必须分配到三级分类，不能只有一级或二级分类！**

### 1. development (开发工具)
- **code-editor** (代码编辑): online-editor, ide, text-editor, code-formatter
- **version-control** (版本控制): git-tools, collaboration, code-review, repository
- **api-tools** (API工具): api-testing, api-docs, mock-tools, api-gateway
- **database** (数据库工具): db-client, db-design, db-migration, db-backup
- **deployment** (部署运维): ci-cd, monitoring, container, cloud-platform
- **debugging** (调试测试): debugger, unit-testing, integration-testing, performance-testing

### 2. design (设计工具)
- **ui-design** (UI设计): prototyping, wireframe, design-system, user-testing
- **graphics** (图形设计): vector-graphics, photo-editing, illustration, logo-design
- **color-tools** (色彩工具): color-picker, color-palette, gradient, color-theory
- **icon-fonts** (图标字体): icon-library, font-tools, emoji, typography
- **3d-design** (3D设计): 3d-modeling, 3d-rendering, 3d-animation, 3d-printing
- **multimedia** (多媒体设计): video-editing, audio-editing, motion-graphics, presentation

### 3. productivity (效率工具)
- **note-taking** (笔记工具): markdown, knowledge-base, mind-map, note-sync
- **task-management** (任务管理): todo-list, project-management, time-tracking, team-collaboration
- **automation** (自动化工具): workflow, scripting, integration, scheduling
- **office-tools** (办公工具): document, spreadsheet, presentation, pdf-tools
- **time-management** (时间管理): pomodoro, time-tracking, scheduling, efficiency-analysis
- **communication** (沟通协作): instant-messaging, video-conferencing, file-sharing, collaborative-whiteboard

### 4. learning (学习资源)
- **programming** (编程学习): coding-practice, algorithm, tutorial, code-challenge
- **language** (语言学习): vocabulary, grammar, pronunciation, conversation
- **reference** (参考资料): documentation, cheatsheet, examples, wiki
- **online-courses** (在线课程): mooc, video-courses, certification, live-courses
- **skill-training** (技能培训): professional-skills, soft-skills, hands-on-training, skill-assessment
- **academic-research** (学术研究): paper-writing, literature-management, data-collection, research-tools

### 5. entertainment (娱乐工具)
- **media-streaming** (媒体播放): video-streaming, music-streaming, podcast, live-streaming
- **games** (游戏娱乐): online-games, game-tools, game-community, mobile-games
- **content-download** (内容下载): video-download, music-download, movie-download, ebook-download
- **social-entertainment** (社交娱乐): social-media, chat-tools, community, dating-apps
- **creative-entertainment** (创意娱乐): creation-platform, art-creation, music-creation, writing-creation
- **interactive-entertainment** (体感娱乐): vr-experience, ar-application, motion-games, interactive-fun

### 6. life-service (生活服务)
- **daily-tools** (日常工具): weather, calendar, reminder, alarm-clock
- **travel** (旅行出行): map-navigation, booking, travel-guide, transportation
- **health-fitness** (健康健身): fitness-tracker, health-monitor, nutrition, medical-info
- **finance** (金融理财): budget-tracker, investment, currency, banking
- **shopping** (购物消费): price-comparison, coupons, shopping-assistant, expense-tracking
- **home-living** (家居生活): smart-home, household-management, home-decoration, life-tips

### 7. business (商业工具)
- **marketing** (营销推广): seo-tools, social-marketing, email-marketing, content-marketing
- **analytics** (数据分析): web-analytics, business-intelligence, data-visualization, market-research
- **customer-service** (客户服务): live-chat, help-desk, feedback, crm-system
- **e-commerce** (电子商务): online-store, payment, inventory, logistics
- **human-resources** (人力资源): recruitment, employee-management, training-development, performance-evaluation
- **financial-management** (财务管理): accounting, invoice-management, cost-control, financial-reports

### 8. system (系统工具)
- **network-tools** (网络工具): speed-test, dns-tools, proxy-vpn, network-monitor
- **security** (安全工具): password-manager, encryption, security-scan, antivirus
- **file-tools** (文件工具): file-converter, file-compress, file-recovery, file-sync
- **system-monitor** (系统监控): performance, resource-usage, system-info, disk-cleanup
- **system-optimization** (系统优化): startup-optimization, memory-cleanup, registry-cleanup, system-acceleration
- **hardware-tools** (硬件工具): hardware-detection, driver-management, temperature-monitoring, hardware-testing

### 9. ai-tools (AI工具)
- **text-generation** (文本生成): chatbot, writing-assistant, content-creation, translation
- **image-generation** (图像生成): ai-art, photo-enhancement, image-editing, avatar-generator
- **code-assistant** (代码助手): code-completion, code-review, bug-detection, code-generation
- **data-analysis** (数据分析): data-mining, predictive-analysis, pattern-recognition, ai-research
- **voice-processing** (语音处理): speech-recognition, text-to-speech, voice-translation, voice-analysis
- **intelligent-decision** (智能决策): decision-support, risk-assessment, intelligent-recommendation, strategy-optimization

### 10. other (其他工具)
- **utility** (实用工具): calculator, unit-converter, qr-generator, random-generator
- **generator** (生成工具): text-generator, image-generator, data-generator, password-generator
- **testing** (测试工具): website-test, performance-test, compatibility-test, load-test
- **converter** (转换工具): format-converter, encoding-converter, time-converter, currency-converter
- **development-helper** (开发辅助): code-snippets, dev-documentation, dev-environment, dev-resources
- **fun-tools** (趣味工具): prank-tools, fun-tests, creative-generator, mini-games

## 响应格式：
请严格按照以下JSON格式返回：
{
  "searchSummary": "对用户查询的理解和搜索范围说明（30-50字）",
  "recommendedTools": [
    {
      "name": "工具名称",
      "url": "https://官方网站地址",
      "description": "详细的工具描述（80-120字，说明主要功能和特点）",
      "tags": ["相关标签1", "相关标签2", "相关标签3", "相关标签4"],
      "category": "一级分类ID",
      "subcategory": "二级分类ID",
      "subsubcategory": "三级分类ID",
      "relevanceScore": 0.95,
      "recommendReason": "推荐理由（40-60字，说明为什么推荐这个工具）",
      "isGlobalSearch": true
    }
  ],
  "searchInsights": "全网搜索洞察和建议（60-100字）"
}

## 重要要求：
1. **真实工具**：只推荐真实存在、可正常访问的工具
2. **准确信息**：确保工具名称、网址、描述的准确性
3. **完整三级分类**：必须提供完整的category、subcategory、subsubcategory，不能缺失任何一级
4. **分类准确性**：严格按照上述分类体系选择，确保三级分类ID完全匹配
5. **标签丰富**：提供3-5个相关的中文标签
6. **质量优先**：推荐3-6个最优质的工具，避免数量过多

## 分类验证示例：
- ✅ 正确：category: "entertainment", subcategory: "media-streaming", subsubcategory: "video-streaming"
- ❌ 错误：category: "entertainment", subcategory: "media-streaming", subsubcategory: "" (缺失三级分类)
- ❌ 错误：category: "entertainment", subcategory: "", subsubcategory: "video-streaming" (缺失二级分类)`

      const userPrompt = `用户全网搜索查询：${query}

请在全网范围内搜索和推荐与此查询最相关的优秀工具。
重点关注工具的实用性、可靠性和用户体验。
请提供准确的官方网站地址和详细的功能描述。`

      console.log('全网搜索调试信息:')
      console.log('查询:', query)

      const aiResponse = await this.callDeepSeekAPI(systemPrompt, userPrompt, 0.8, 2500, 'global-search', `global-${query.substring(0, 20)}`)
      const result = this.parseAIResponse(aiResponse)

      console.log('全网搜索AI原始返回:', result)

      // 处理AI可能返回的不同格式
      let finalResult

      if (Array.isArray(result)) {
        // 如果AI直接返回了推荐工具数组
        console.log('AI返回了数组格式，转换为对象格式')
        finalResult = {
          searchSummary: `为您的全网搜索找到了 ${result.length} 个优秀工具`,
          recommendedTools: result,
          searchInsights: `基于全网搜索分析，我们为您推荐了 ${result.length} 个最优质的工具，按相关性和质量排序`
        }
      } else if (result && typeof result === 'object') {
        // 如果AI返回了完整的对象格式
        finalResult = result
      } else {
        throw new Error('AI返回的结果格式无效')
      }

      // 确保必要字段存在
      if (!finalResult.searchSummary) {
        finalResult.searchSummary = '全网搜索分析'
      }
      if (!finalResult.searchInsights) {
        finalResult.searchInsights = '暂无搜索洞察'
      }
      if (!finalResult.recommendedTools || !Array.isArray(finalResult.recommendedTools)) {
        finalResult.recommendedTools = []
      }

      // 验证和处理推荐工具，确保分类完整性
      finalResult.recommendedTools = finalResult.recommendedTools.map((tool, index) => {
        // 验证三级分类是否完整和有效
        if (!tool.category || !tool.subcategory || !tool.subsubcategory) {
          console.warn(`工具 "${tool.name}" 分类不完整:`, {
            category: tool.category,
            subcategory: tool.subcategory,
            subsubcategory: tool.subsubcategory
          })

          // 如果分类不完整，使用默认的other分类
          tool.category = tool.category || 'other'
          tool.subcategory = tool.subcategory || 'utility'
          tool.subsubcategory = tool.subsubcategory || 'text-tools'
        } else if (!this.validateCategory(tool.category, tool.subcategory, tool.subsubcategory)) {
          console.warn(`工具 "${tool.name}" 分类无效:`, {
            category: tool.category,
            subcategory: tool.subcategory,
            subsubcategory: tool.subsubcategory
          })

          // 如果分类无效，使用默认的other分类
          tool.category = 'other'
          tool.subcategory = 'utility'
          tool.subsubcategory = 'text-tools'
        }

        return {
          id: `global_${Date.now()}_${index}`, // 生成临时ID
          name: tool.name || '未知工具',
          url: tool.url || '',
          description: tool.description || '暂无描述',
          tags: Array.isArray(tool.tags) ? tool.tags : [],
          category: tool.category,
          subcategory: tool.subcategory,
          subsubcategory: tool.subsubcategory,
          addedAt: new Date().toISOString(),
          sensitive: false,
          relevanceScore: tool.relevanceScore || 0.8,
          recommendReason: tool.recommendReason || '推荐理由暂无',
          isGlobalSearch: true // 标识这是全网搜索的结果
        }
      })

      console.log('全网搜索最终处理结果:', finalResult)
      return finalResult
    } catch (error) {
      console.error('全网搜索失败:', error)
      throw error
    }
  }
}
