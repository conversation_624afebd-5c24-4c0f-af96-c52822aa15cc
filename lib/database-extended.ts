import { supabase } from './supabase'
import { Tool } from './types'

// 扩展数据库操作类
export class ExtendedDatabase {

  // ==================== 操作日志相关 ====================

  static async createOperationLog(
    action: string,
    operationType: string,
    targetType?: string,
    targetId?: string,
    details?: any,
    status: 'success' | 'error' | 'warning' | 'pending' = 'success',
    errorMessage?: string
  ) {
    try {
      const { error } = await supabase
        .from('operation_logs')
        .insert([{
          action,
          operation_type: operationType,
          target_type: targetType,
          target_id: targetId,
          details: details || {},
          status,
          error_message: errorMessage,
          ip_address: null, // 客户端无法获取真实IP
          user_agent: typeof window !== 'undefined' ? navigator.userAgent : null
        }])

      if (error) {
        console.error('创建操作日志失败:', error)
      }
    } catch (error) {
      console.error('创建操作日志异常:', error)
    }
  }

  static async getOperationLogs(limit: number = 50, offset: number = 0) {
    try {
      const { data, error } = await supabase
        .from('operation_logs')
        .select('*')
        .order('created_at', { ascending: false })
        .range(offset, offset + limit - 1)

      if (error) throw error
      return data || []
    } catch (error) {
      console.error('获取操作日志失败:', error)
      return []
    }
  }

  static async clearOperationLogs() {
    try {
      const { error } = await supabase
        .from('operation_logs')
        .delete()
        .neq('id', '00000000-0000-0000-0000-000000000000') // 删除所有记录

      if (error) throw error
      return true
    } catch (error) {
      console.error('清理操作日志失败:', error)
      return false
    }
  }

  // ==================== 批量导入相关 ====================

  static async createImportTask(
    taskName: string,
    fileName?: string,
    fileSize?: number,
    fileType?: string,
    contentPreview?: string
  ) {
    try {
      const { data, error } = await supabase
        .from('import_tasks')
        .insert([{
          task_name: taskName,
          file_name: fileName,
          file_size: fileSize,
          file_type: fileType,
          content_preview: contentPreview?.substring(0, 1000)
        }])
        .select()
        .single()

      if (error) throw error
      return data
    } catch (error) {
      console.error('创建导入任务失败:', error)
      return null
    }
  }

  static async updateImportTask(
    taskId: string,
    updates: {
      totalItems?: number
      processedItems?: number
      successItems?: number
      failedItems?: number
      duplicateItems?: number
      status?: string
      progressPercentage?: number
      errorDetails?: any
      resultSummary?: any
      startedAt?: string
      completedAt?: string
    }
  ) {
    try {
      const updateData: any = {}

      if (updates.totalItems !== undefined) updateData.total_items = updates.totalItems
      if (updates.processedItems !== undefined) updateData.processed_items = updates.processedItems
      if (updates.successItems !== undefined) updateData.success_items = updates.successItems
      if (updates.failedItems !== undefined) updateData.failed_items = updates.failedItems
      if (updates.duplicateItems !== undefined) updateData.duplicate_items = updates.duplicateItems
      if (updates.status) updateData.status = updates.status
      if (updates.progressPercentage !== undefined) updateData.progress_percentage = updates.progressPercentage
      if (updates.errorDetails) updateData.error_details = updates.errorDetails
      if (updates.resultSummary) updateData.result_summary = updates.resultSummary
      if (updates.startedAt) updateData.started_at = updates.startedAt
      if (updates.completedAt) updateData.completed_at = updates.completedAt

      const { error } = await supabase
        .from('import_tasks')
        .update(updateData)
        .eq('id', taskId)

      if (error) throw error
      return true
    } catch (error) {
      console.error('更新导入任务失败:', error)
      return false
    }
  }

  static async getImportTasks(limit: number = 20) {
    try {
      const { data, error } = await supabase
        .from('import_tasks')
        .select('*')
        .order('created_at', { ascending: false })
        .limit(limit)

      if (error) throw error
      return data || []
    } catch (error) {
      console.error('获取导入任务失败:', error)
      return []
    }
  }

  static async createImportTaskItem(
    taskId: string,
    itemIndex: number,
    originalData: any,
    processedData?: any,
    toolId?: string,
    status: string = 'pending',
    errorMessage?: string,
    aiAnalysisResult?: any,
    processingTimeMs?: number
  ) {
    try {
      const { error } = await supabase
        .from('import_task_items')
        .insert([{
          task_id: taskId,
          item_index: itemIndex,
          original_data: originalData,
          processed_data: processedData,
          tool_id: toolId,
          status,
          error_message: errorMessage,
          ai_analysis_result: aiAnalysisResult,
          processing_time_ms: processingTimeMs
        }])

      if (error) throw error
      return true
    } catch (error) {
      console.error('创建导入任务项失败:', error)
      return false
    }
  }

  // ==================== 数据清理相关 ====================

  static async createCleanupReport(
    operationType: string,
    originalCount: number,
    processedCount: number,
    removedCount: number,
    details: any
  ) {
    try {
      const { data, error } = await supabase
        .from('cleanup_reports')
        .insert([{
          operation_type: operationType,
          original_count: originalCount,
          processed_count: processedCount,
          removed_count: removedCount,
          details
        }])
        .select()
        .single()

      if (error) throw error
      return data
    } catch (error) {
      console.error('创建清理报告失败:', error)
      return null
    }
  }

  static async getCleanupReports(limit: number = 20) {
    try {
      const { data, error } = await supabase
        .from('cleanup_reports')
        .select('*')
        .order('created_at', { ascending: false })
        .limit(limit)

      if (error) throw error
      return data || []
    } catch (error) {
      console.error('获取清理报告失败:', error)
      return []
    }
  }

  // ==================== AI分析缓存相关 ====================

  static async getAIAnalysisCache(urlHash: string) {
    try {
      const { data, error } = await supabase
        .from('ai_analysis_cache')
        .select('*')
        .eq('url_hash', urlHash)
        .single()

      if (error && error.code !== 'PGRST116') throw error // PGRST116 是没有找到记录的错误
      return data
    } catch (error) {
      console.error('获取AI分析缓存失败:', error)
      return null
    }
  }

  static async setAIAnalysisCache(
    urlHash: string,
    originalUrl: string,
    analysisResult: any,
    confidenceScore?: number,
    analysisTimeMs?: number
  ) {
    try {
      const { error } = await supabase
        .from('ai_analysis_cache')
        .upsert([{
          url_hash: urlHash,
          original_url: originalUrl,
          analysis_result: analysisResult,
          confidence_score: confidenceScore,
          analysis_time_ms: analysisTimeMs,
          updated_at: new Date().toISOString()
        }])

      if (error) throw error
      return true
    } catch (error) {
      console.error('设置AI分析缓存失败:', error)
      return false
    }
  }

  // ==================== 数据统计相关 ====================

  static async getDataStatistics(statType: string, statKey: string) {
    try {
      const { data, error } = await supabase
        .from('data_statistics')
        .select('*')
        .eq('stat_type', statType)
        .eq('stat_key', statKey)
        .single()

      if (error && error.code !== 'PGRST116') throw error
      return data
    } catch (error) {
      console.error('获取数据统计失败:', error)
      return null
    }
  }

  static async setDataStatistics(
    statType: string,
    statKey: string,
    statValue: any,
    expiresAt?: string
  ) {
    try {
      const { error } = await supabase
        .from('data_statistics')
        .upsert([{
          stat_type: statType,
          stat_key: statKey,
          stat_value: statValue,
          expires_at: expiresAt,
          calculated_at: new Date().toISOString()
        }], {
          onConflict: 'stat_type,stat_key'
        })

      if (error) throw error
      return true
    } catch (error) {
      console.error('设置数据统计失败:', error)
      return false
    }
  }

  static async calculateDailyStatistics(tools: Tool[]) {
    const today = new Date().toISOString().split('T')[0]
    const yesterday = new Date(Date.now() - 24 * 60 * 60 * 1000).toISOString().split('T')[0]
    const thisMonth = new Date().toISOString().substring(0, 7)

    // 今日新增工具
    const todayTools = tools.filter(tool =>
      tool.addedAt.startsWith(today)
    ).length

    // 昨日新增工具
    const yesterdayTools = tools.filter(tool =>
      tool.addedAt.startsWith(yesterday)
    ).length

    // 本月新增工具
    const thisMonthTools = tools.filter(tool =>
      tool.addedAt.startsWith(thisMonth)
    ).length

    // 分类统计
    const categoryStats = tools.reduce((acc, tool) => {
      acc[tool.category] = (acc[tool.category] || 0) + 1
      return acc
    }, {} as Record<string, number>)

    // 标签统计
    const tagStats = tools.reduce((acc, tool) => {
      tool.tags.forEach(tag => {
        acc[tag] = (acc[tag] || 0) + 1
      })
      return acc
    }, {} as Record<string, number>)

    const statistics = {
      total: tools.length,
      today: todayTools,
      yesterday: yesterdayTools,
      thisMonth: thisMonthTools,
      categories: categoryStats,
      tags: tagStats,
      topCategories: Object.entries(categoryStats)
        .sort(([,a], [,b]) => b - a)
        .slice(0, 10),
      topTags: Object.entries(tagStats)
        .sort(([,a], [,b]) => b - a)
        .slice(0, 20),
      calculatedAt: new Date().toISOString()
    }

    // 缓存统计数据（1小时过期）
    const expiresAt = new Date(Date.now() + 60 * 60 * 1000).toISOString()
    await this.setDataStatistics('daily', today, statistics, expiresAt)

    return statistics
  }
}
