#!/usr/bin/env node

/**
 * ToolMaster 存储监控功能验证脚本
 * 
 * 用途：验证存储监控功能的各个组件是否正确实现
 * 运行：node scripts/verify-storage-monitoring.js
 */

const fs = require('fs');
const path = require('path');

console.log('🗄️ ToolMaster 存储监控功能验证');
console.log('=====================================\n');

let passCount = 0;
let totalCount = 0;

function test(description, testFn) {
    totalCount++;
    try {
        const result = testFn();
        if (result) {
            console.log(`✅ ${description}`);
            passCount++;
        } else {
            console.log(`❌ ${description}`);
        }
    } catch (error) {
        console.log(`❌ ${description} - 错误: ${error.message}`);
    }
}

function fileExists(filePath) {
    return fs.existsSync(path.join(__dirname, '..', filePath));
}

function fileContains(filePath, searchText) {
    if (!fileExists(filePath)) return false;
    const content = fs.readFileSync(path.join(__dirname, '..', filePath), 'utf8');
    return content.includes(searchText);
}

console.log('📁 文件结构验证');
console.log('-------------------');

test('存储监控服务文件存在', () => {
    return fileExists('lib/storage-monitoring-service.ts');
});

test('SQL 函数脚本文件存在', () => {
    return fileExists('sql/storage-monitoring-functions.sql');
});

test('功能文档文件存在', () => {
    return fileExists('doc/STORAGE_MONITORING_GUIDE.md');
});

test('测试页面文件存在', () => {
    return fileExists('test/storage-monitoring-test.html');
});

console.log('\n🔧 代码实现验证');
console.log('-------------------');

test('StorageMonitoringService 类定义正确', () => {
    return fileContains('lib/storage-monitoring-service.ts', 'export class StorageMonitoringService');
});

test('StorageUsageData 接口定义正确', () => {
    return fileContains('lib/storage-monitoring-service.ts', 'export interface StorageUsageData');
});

test('getStorageUsage 方法存在', () => {
    return fileContains('lib/storage-monitoring-service.ts', 'static async getStorageUsage()');
});

test('clearCache 方法存在', () => {
    return fileContains('lib/storage-monitoring-service.ts', 'static async clearCache()');
});

test('容量预估功能实现', () => {
    return fileContains('lib/storage-monitoring-service.ts', 'calculateProjections');
});

console.log('\n📊 SQL 函数验证');
console.log('-------------------');

test('数据库大小查询函数', () => {
    return fileContains('sql/storage-monitoring-functions.sql', 'CREATE OR REPLACE FUNCTION get_database_size()');
});

test('WAL 大小查询函数', () => {
    return fileContains('sql/storage-monitoring-functions.sql', 'CREATE OR REPLACE FUNCTION get_wal_size()');
});

test('表大小查询函数', () => {
    return fileContains('sql/storage-monitoring-functions.sql', 'CREATE OR REPLACE FUNCTION get_table_sizes()');
});

test('权限设置正确', () => {
    return fileContains('sql/storage-monitoring-functions.sql', 'GRANT EXECUTE ON FUNCTION') &&
           fileContains('sql/storage-monitoring-functions.sql', 'TO anon');
});

console.log('\n🎨 UI 组件验证');
console.log('-------------------');

test('数据管理组件导入存储监控服务', () => {
    return fileContains('components/enhanced-data-management.tsx', 'StorageMonitoringService');
});

test('存储监控状态管理', () => {
    return fileContains('components/enhanced-data-management.tsx', 'useState<StorageUsageData');
});

test('存储监控标签页存在', () => {
    return fileContains('components/enhanced-data-management.tsx', 'value="storage"');
});

test('loadStorageUsage 函数存在', () => {
    return fileContains('components/enhanced-data-management.tsx', 'const loadStorageUsage');
});

test('存储监控 UI 组件完整', () => {
    return fileContains('components/enhanced-data-management.tsx', 'Database') &&
           fileContains('components/enhanced-data-management.tsx', 'HardDrive') &&
           fileContains('components/enhanced-data-management.tsx', 'TrendingUp');
});

console.log('\n📋 功能特性验证');
console.log('-------------------');

test('缓存机制实现', () => {
    return fileContains('lib/storage-monitoring-service.ts', 'ExtendedDatabase.getDataStatistics') &&
           fileContains('lib/storage-monitoring-service.ts', '5 * 60 * 1000');
});

test('错误处理机制', () => {
    return fileContains('lib/storage-monitoring-service.ts', 'try {') &&
           fileContains('lib/storage-monitoring-service.ts', 'catch (error)');
});

test('容量告警功能', () => {
    return fileContains('components/enhanced-data-management.tsx', 'currentUsagePercentage > 80');
});

test('字节格式化功能', () => {
    return fileContains('lib/storage-monitoring-service.ts', 'formatBytes');
});

test('表大小估算功能', () => {
    return fileContains('lib/storage-monitoring-service.ts', 'estimateTableSizes');
});

console.log('\n📖 文档验证');
console.log('-------------------');

test('功能概述文档完整', () => {
    return fileContains('doc/STORAGE_MONITORING_GUIDE.md', '功能概述') &&
           fileContains('doc/STORAGE_MONITORING_GUIDE.md', '核心功能');
});

test('快速开始指南存在', () => {
    return fileContains('doc/STORAGE_MONITORING_GUIDE.md', '快速开始');
});

test('技术实现说明完整', () => {
    return fileContains('doc/STORAGE_MONITORING_GUIDE.md', '技术实现') &&
           fileContains('doc/STORAGE_MONITORING_GUIDE.md', 'SQL 查询');
});

test('故障排除指南存在', () => {
    return fileContains('doc/STORAGE_MONITORING_GUIDE.md', '故障排除');
});

console.log('\n🧪 测试文件验证');
console.log('-------------------');

test('测试页面结构完整', () => {
    return fileContains('test/storage-monitoring-test.html', 'runStorageTest') &&
           fileContains('test/storage-monitoring-test.html', 'runPerformanceTest');
});

test('测试日志功能存在', () => {
    return fileContains('test/storage-monitoring-test.html', 'function log(');
});

test('模拟数据结构正确', () => {
    return fileContains('test/storage-monitoring-test.html', 'mockStorageData') &&
           fileContains('test/storage-monitoring-test.html', 'projections');
});

console.log('\n📦 依赖和导入验证');
console.log('-------------------');

test('Supabase 客户端导入', () => {
    return fileContains('lib/storage-monitoring-service.ts', "import { supabase } from './supabase'");
});

test('ExtendedDatabase 导入', () => {
    return fileContains('lib/storage-monitoring-service.ts', "import { ExtendedDatabase } from './database-extended'");
});

test('UI 图标导入正确', () => {
    return fileContains('components/enhanced-data-management.tsx', 'Database,') &&
           fileContains('components/enhanced-data-management.tsx', 'HardDrive,') &&
           fileContains('components/enhanced-data-management.tsx', 'TrendingUp');
});

console.log('\n📊 验证结果汇总');
console.log('=====================================');
console.log(`总测试项目: ${totalCount}`);
console.log(`通过测试: ${passCount}`);
console.log(`失败测试: ${totalCount - passCount}`);
console.log(`通过率: ${((passCount / totalCount) * 100).toFixed(1)}%`);

if (passCount === totalCount) {
    console.log('\n🎉 所有验证项目都通过了！');
    console.log('✅ 存储监控功能实现完整，可以正常使用。');
    console.log('\n📋 下一步操作：');
    console.log('1. 在 Supabase SQL Editor 中执行 sql/storage-monitoring-functions.sql');
    console.log('2. 启动应用并测试存储监控功能');
    console.log('3. 查看 doc/STORAGE_MONITORING_GUIDE.md 了解详细使用方法');
    console.log('4. 使用 test/storage-monitoring-test.html 进行功能测试');
} else {
    console.log('\n⚠️  部分验证项目未通过，请检查实现。');
    console.log('❌ 请根据失败的测试项目检查对应的代码实现。');
}

console.log('\n🔗 相关文件：');
console.log('- 核心服务: lib/storage-monitoring-service.ts');
console.log('- SQL 脚本: sql/storage-monitoring-functions.sql');
console.log('- UI 组件: components/enhanced-data-management.tsx');
console.log('- 使用文档: doc/STORAGE_MONITORING_GUIDE.md');
console.log('- 测试页面: test/storage-monitoring-test.html');

process.exit(passCount === totalCount ? 0 : 1);
