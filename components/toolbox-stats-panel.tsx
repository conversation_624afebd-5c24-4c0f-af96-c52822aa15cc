"use client"

import Re<PERSON>, { useState, useEffect } from "react"
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { But<PERSON> } from "@/components/ui/button"
import { Separator } from "@/components/ui/separator"
import { 
  BarChart3, 
  TrendingUp, 
  Clock, 
  Target,
  Download,
  RefreshCw,
  Globe
} from "lucide-react"
import { ToolboxAnalytics, type ToolboxStats } from "@/lib/toolbox-analytics"

interface ToolboxStatsPanelProps {
  className?: string
}

export function ToolboxStatsPanel({ className }: ToolboxStatsPanelProps) {
  const [stats, setStats] = useState<ToolboxStats | null>(null)
  const [todayStats, setTodayStats] = useState<any>(null)
  const [popularDomains, setPopularDomains] = useState<Array<{ domain: string; count: number }>>([])
  const [isLoading, setIsLoading] = useState(true)

  const loadStats = () => {
    setIsLoading(true)
    try {
      const monthlyStats = ToolboxAnalytics.getStats(30)
      const dailyStats = ToolboxAnalytics.getTodayStats()
      const domains = ToolboxAnalytics.getPopularDomains(5)
      
      setStats(monthlyStats)
      setTodayStats(dailyStats)
      setPopularDomains(domains)
    } catch (error) {
      console.error('加载工具箱统计失败:', error)
    } finally {
      setIsLoading(false)
    }
  }

  useEffect(() => {
    loadStats()
  }, [])

  const handleExportStats = () => {
    try {
      const exportData = ToolboxAnalytics.exportStats()
      const blob = new Blob([exportData], { type: 'application/json' })
      const url = URL.createObjectURL(blob)
      const a = document.createElement('a')
      a.href = url
      a.download = `toolbox-stats-${new Date().toISOString().split('T')[0]}.json`
      document.body.appendChild(a)
      a.click()
      document.body.removeChild(a)
      URL.revokeObjectURL(url)
    } catch (error) {
      console.error('导出统计数据失败:', error)
    }
  }

  if (isLoading) {
    return (
      <Card className={className}>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <BarChart3 className="h-5 w-5" />
            工具箱使用统计
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="flex items-center justify-center py-8">
            <RefreshCw className="h-6 w-6 animate-spin" />
          </div>
        </CardContent>
      </Card>
    )
  }

  if (!stats || !todayStats) {
    return (
      <Card className={className}>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <BarChart3 className="h-5 w-5" />
            工具箱使用统计
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="text-center py-8 text-muted-foreground">
            暂无使用数据
          </div>
        </CardContent>
      </Card>
    )
  }

  return (
    <Card className={className}>
      <CardHeader>
        <div className="flex items-center justify-between">
          <div>
            <CardTitle className="flex items-center gap-2">
              <BarChart3 className="h-5 w-5" />
              工具箱使用统计
            </CardTitle>
            <CardDescription>
              最近30天的使用情况和性能指标
            </CardDescription>
          </div>
          <div className="flex gap-2">
            <Button variant="outline" size="sm" onClick={loadStats}>
              <RefreshCw className="h-4 w-4 mr-2" />
              刷新
            </Button>
            <Button variant="outline" size="sm" onClick={handleExportStats}>
              <Download className="h-4 w-4 mr-2" />
              导出
            </Button>
          </div>
        </div>
      </CardHeader>
      <CardContent className="space-y-6">
        {/* 今日统计 */}
        <div>
          <h4 className="text-sm font-medium mb-3 flex items-center gap-2">
            <TrendingUp className="h-4 w-4" />
            今日使用情况
          </h4>
          <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
            <div className="text-center">
              <div className="text-2xl font-bold text-primary">{todayStats.extractions}</div>
              <div className="text-xs text-muted-foreground">分析次数</div>
            </div>
            <div className="text-center">
              <div className="text-2xl font-bold text-green-600">{todayStats.toolsExtracted}</div>
              <div className="text-xs text-muted-foreground">提取工具</div>
            </div>
            <div className="text-center">
              <div className="text-2xl font-bold text-blue-600">{todayStats.successRate}%</div>
              <div className="text-xs text-muted-foreground">成功率</div>
            </div>
            <div className="text-center">
              <div className="text-2xl font-bold text-purple-600">{todayStats.averageTime}ms</div>
              <div className="text-xs text-muted-foreground">平均耗时</div>
            </div>
          </div>
        </div>

        <Separator />

        {/* 月度统计 */}
        <div>
          <h4 className="text-sm font-medium mb-3 flex items-center gap-2">
            <Target className="h-4 w-4" />
            月度统计（最近30天）
          </h4>
          <div className="grid grid-cols-2 md:grid-cols-3 gap-4">
            <div className="text-center">
              <div className="text-xl font-bold">{stats.totalUsage}</div>
              <div className="text-xs text-muted-foreground">总使用次数</div>
            </div>
            <div className="text-center">
              <div className="text-xl font-bold text-green-600">{stats.successfulExtractions}</div>
              <div className="text-xs text-muted-foreground">成功提取</div>
            </div>
            <div className="text-center">
              <div className="text-xl font-bold text-blue-600">{stats.totalToolsExtracted}</div>
              <div className="text-xs text-muted-foreground">总提取工具数</div>
            </div>
          </div>
        </div>

        <Separator />

        {/* 性能指标 */}
        <div>
          <h4 className="text-sm font-medium mb-3 flex items-center gap-2">
            <Clock className="h-4 w-4" />
            性能指标
          </h4>
          <div className="space-y-2">
            <div className="flex justify-between items-center">
              <span className="text-sm text-muted-foreground">平均处理时间</span>
              <Badge variant="outline">{stats.averageProcessingTime}ms</Badge>
            </div>
            <div className="flex justify-between items-center">
              <span className="text-sm text-muted-foreground">成功率</span>
              <Badge variant="outline">
                {stats.totalUsage > 0 
                  ? Math.round((stats.successfulExtractions / stats.totalUsage) * 100)
                  : 0}%
              </Badge>
            </div>
          </div>
        </div>

        {/* 热门域名 */}
        {popularDomains.length > 0 && (
          <>
            <Separator />
            <div>
              <h4 className="text-sm font-medium mb-3 flex items-center gap-2">
                <Globe className="h-4 w-4" />
                热门分析域名
              </h4>
              <div className="space-y-2">
                {popularDomains.map((domain, index) => (
                  <div key={domain.domain} className="flex justify-between items-center">
                    <div className="flex items-center gap-2">
                      <Badge variant="secondary" className="text-xs">
                        #{index + 1}
                      </Badge>
                      <span className="text-sm font-mono">{domain.domain}</span>
                    </div>
                    <Badge variant="outline">{domain.count}次</Badge>
                  </div>
                ))}
              </div>
            </div>
          </>
        )}

        {/* 功能使用分布 */}
        {Object.keys(stats.mostUsedFeatures).length > 0 && (
          <>
            <Separator />
            <div>
              <h4 className="text-sm font-medium mb-3">功能使用分布</h4>
              <div className="space-y-2">
                {Object.entries(stats.mostUsedFeatures)
                  .sort(([,a], [,b]) => b - a)
                  .slice(0, 5)
                  .map(([feature, count]) => {
                    const [toolId, action] = feature.split('-')
                    const actionNames: Record<string, string> = {
                      'open': '打开工具',
                      'analyze': '分析网站',
                      'export': '导出结果',
                      'import': '直接导入',
                      'copy': '复制链接'
                    }
                    return (
                      <div key={feature} className="flex justify-between items-center">
                        <span className="text-sm">{actionNames[action] || action}</span>
                        <Badge variant="outline">{count}次</Badge>
                      </div>
                    )
                  })}
              </div>
            </div>
          </>
        )}
      </CardContent>
    </Card>
  )
}
