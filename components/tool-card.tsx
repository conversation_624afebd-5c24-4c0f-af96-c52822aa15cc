"use client"

import { <PERSON>, CardContent, CardDescription, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON><PERSON>er, CardTitle } from "@/components/ui/card"
import { Button } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { Checkbox } from "@/components/ui/checkbox"
import { ExternalLink, Copy, Trash2, MoreVertical, Plus } from "lucide-react"
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuTrigger } from "@/components/ui/dropdown-menu"
import type { Tool } from "@/components/toolbox"
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from "@/components/ui/tooltip"

interface ToolCardProps {
  tool: Tool
  getCategoryPath: (categoryId: string, subcategoryId: string, subsubcategoryId: string) => string
  onCategoryPathClick: (category: string, subcategory: string, subsubCategory: string) => void
  onTagClick: (tag: string) => void
  onDeleteTool: (id: string) => void
  onCopyToolInfo: (type: "name" | "url" | "description", value: string) => void
  onViewDescription: (tool: Tool) => void
  // 深度搜索相关属性
  recommendReason?: string
  relevanceScore?: number
  // 全网搜索相关属性
  isGlobalSearchResult?: boolean
  onAddToLocal?: () => void
  isAlreadyInLocal?: boolean
  // 批量删除相关属性
  isBatchMode?: boolean
  isSelected?: boolean
  onSelectionChange?: (toolId: string, selected: boolean) => void
}

export function ToolCard({
  tool,
  getCategoryPath,
  onCategoryPathClick,
  onTagClick,
  onDeleteTool,
  onCopyToolInfo,
  onViewDescription,
  recommendReason,
  relevanceScore,
  isGlobalSearchResult,
  onAddToLocal,
  isAlreadyInLocal,
  isBatchMode = false,
  isSelected = false,
  onSelectionChange,
}: ToolCardProps) {
  const categoryPath = getCategoryPath(tool.category, tool.subcategory, tool.subsubcategory)

  return (
    <Card className={`flex flex-col h-full hover:shadow-md transition-shadow duration-200 ${isSelected ? 'ring-2 ring-primary' : ''}`}>
      <CardHeader className="pb-3 space-y-2">
        <div className="flex items-start justify-between gap-2">
          {/* 批量模式下显示复选框 */}
          {isBatchMode && (
            <Checkbox
              checked={isSelected}
              onCheckedChange={(checked) => onSelectionChange?.(tool.id, !!checked)}
              className="mt-1 shrink-0"
            />
          )}
          <CardTitle
            className={`text-base font-semibold line-clamp-2 cursor-pointer hover:text-primary transition-colors leading-tight ${isBatchMode ? 'flex-1' : ''}`}
            onClick={() => onViewDescription(tool)}
          >
            {tool.name}
          </CardTitle>
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button variant="ghost" size="icon" className="w-7 h-7 shrink-0">
                <MoreVertical className="h-3.5 w-3.5" />
                <span className="sr-only">更多操作</span>
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent align="end">
              {/* 全网搜索结果特有的添加到本站选项 */}
              {isGlobalSearchResult && (
                isAlreadyInLocal ? (
                  <DropdownMenuItem disabled className="text-gray-500">
                    <Plus className="mr-2 h-4 w-4" /> 已添加到本站
                  </DropdownMenuItem>
                ) : onAddToLocal ? (
                  <DropdownMenuItem onClick={onAddToLocal} className="text-green-600">
                    <Plus className="mr-2 h-4 w-4" /> 添加到本站
                  </DropdownMenuItem>
                ) : null
              )}

              <DropdownMenuItem onClick={() => onCopyToolInfo("name", tool.name)}>
                <Copy className="mr-2 h-4 w-4" /> 复制名称
              </DropdownMenuItem>
              <DropdownMenuItem onClick={() => onCopyToolInfo("url", tool.url)}>
                <Copy className="mr-2 h-4 w-4" /> 复制链接
              </DropdownMenuItem>
              <DropdownMenuItem onClick={() => onCopyToolInfo("description", tool.description)}>
                <Copy className="mr-2 h-4 w-4" /> 复制描述
              </DropdownMenuItem>

              {/* 只有非全网搜索结果才显示删除选项 */}
              {!isGlobalSearchResult && (
                <DropdownMenuItem onClick={() => onDeleteTool(tool.id)} className="text-red-600">
                  <Trash2 className="mr-2 h-4 w-4" /> 删除工具
                </DropdownMenuItem>
              )}
            </DropdownMenuContent>
          </DropdownMenu>
        </div>
        <CardDescription
          className="text-xs text-muted-foreground line-clamp-2 cursor-pointer hover:text-foreground transition-colors leading-relaxed"
          onClick={() => onViewDescription(tool)}
        >
          {tool.description}
        </CardDescription>
      </CardHeader>
      <CardContent className="flex-1 py-2 space-y-2">
        {/* AI推荐理由显示 */}
        {recommendReason && (
          <div className="bg-gradient-to-r from-blue-50 to-purple-50 dark:from-blue-950/20 dark:to-purple-950/20 rounded-md p-2 mb-2">
            <div className="flex items-center gap-1 mb-1">
              <div className="w-1.5 h-1.5 bg-gradient-to-r from-blue-500 to-purple-600 rounded-full"></div>
              <span className="text-xs font-medium text-blue-700 dark:text-blue-300">推荐理由</span>
              {relevanceScore && (
                <Badge variant="secondary" className="text-xs ml-auto">
                  匹配度 {Math.round(relevanceScore * 100)}%
                </Badge>
              )}
            </div>
            <p className="text-xs text-muted-foreground leading-relaxed">{recommendReason}</p>
          </div>
        )}

        <div className="flex flex-wrap gap-1">
          <TooltipProvider>
            <Tooltip>
              <TooltipTrigger asChild>
                <Badge
                  variant="outline"
                  className="text-xs cursor-pointer hover:bg-accent transition-colors"
                  onClick={() => onCategoryPathClick(tool.category, tool.subcategory, tool.subsubcategory)}
                >
                  {categoryPath}
                </Badge>
              </TooltipTrigger>
              <TooltipContent>
                <p>点击筛选此分类</p>
              </TooltipContent>
            </Tooltip>
          </TooltipProvider>
        </div>
        <div className="flex flex-wrap gap-1">
          {tool.tags.slice(0, 4).map((tag) => (
            <TooltipProvider key={tag}>
              <Tooltip>
                <TooltipTrigger asChild>
                  <Badge
                    variant="secondary"
                    className="text-xs cursor-pointer hover:bg-primary hover:text-primary-foreground transition-colors"
                    onClick={() => onTagClick(tag)}
                  >
                    {tag}
                  </Badge>
                </TooltipTrigger>
                <TooltipContent>
                  <p>点击筛选标签</p>
                </TooltipContent>
              </Tooltip>
            </TooltipProvider>
          ))}
          {tool.tags.length > 4 && (
            <Badge variant="secondary" className="text-xs">
              +{tool.tags.length - 4}
            </Badge>
          )}
        </div>
      </CardContent>
      <CardFooter className="pt-2 pb-3">
        <Button asChild className="w-full h-8 text-sm">
          <a href={tool.url} target="_blank" rel="noopener noreferrer">
            访问工具
            <ExternalLink className="ml-2 h-3.5 w-3.5" />
          </a>
        </Button>
      </CardFooter>
    </Card>
  )
}
