"use client"

import React, { useState } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog"
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import {
  Wrench,
  Globe,
  Download,
  Zap,
  ArrowLeft,
  BarChart3,
  Sparkles,
  HelpCircle
} from "lucide-react"
import { WebsiteToolExtractor } from "./website-tool-extractor"
import { ToolboxStatsPanel } from "./toolbox-stats-panel"
import { ToolboxHelp } from "./toolbox-help"

// 工具箱中的工具列表
const TOOLBOX_TOOLS = [
  {
    id: "website-tool-extractor",
    name: "网站工具提取",
    description: "智能分析网站并提取其中的工具链接，支持导航网站和工具集合网站",
    icon: Globe,
    category: "数据采集",
    status: "active" as const,
    features: ["AI智能识别", "批量提取", "一键导出", "去重验证"]
  },
  {
    id: "url-batch-validator",
    name: "URL批量验证",
    description: "批量验证URL的有效性，检测失效链接和重定向",
    icon: Zap,
    category: "数据验证",
    status: "coming-soon" as const,
    features: ["批量检测", "状态码验证", "重定向跟踪", "响应时间"]
  },
  {
    id: "tool-data-converter",
    name: "工具数据转换",
    description: "在不同格式之间转换工具数据，支持JSON、CSV、XML等",
    icon: Download,
    category: "数据转换",
    status: "coming-soon" as const,
    features: ["多格式支持", "自定义映射", "数据清洗", "格式验证"]
  },
  {
    id: "toolbox-analytics",
    name: "使用统计",
    description: "查看工具箱的使用统计和性能指标，了解使用习惯",
    icon: BarChart3,
    category: "数据分析",
    status: "active" as const,
    features: ["使用统计", "性能监控", "热门域名", "数据导出"]
  },
  {
    id: "toolbox-help",
    name: "使用指南",
    description: "详细的使用说明和最佳实践，帮助您更好地使用工具箱",
    icon: HelpCircle,
    category: "帮助文档",
    status: "active" as const,
    features: ["使用教程", "最佳实践", "示例网站", "常见问题"]
  }
]

interface UtilityToolboxProps {
  onClose?: () => void
}

export function UtilityToolbox({ onClose }: UtilityToolboxProps) {
  const [selectedTool, setSelectedTool] = useState<string | null>(null)
  const [isOpen, setIsOpen] = useState(false)

  const handleToolSelect = (toolId: string) => {
    const tool = TOOLBOX_TOOLS.find(t => t.id === toolId)
    if (tool?.status === "active") {
      setSelectedTool(toolId)
    }
  }

  const handleBack = () => {
    setSelectedTool(null)
  }

  const handleClose = () => {
    setIsOpen(false)
    setSelectedTool(null)
    onClose?.()
  }

  const renderToolList = () => (
    <div className="space-y-4">
      <div className="text-center mb-6">
        <h3 className="text-lg font-semibold mb-2">实用工具箱</h3>
        <p className="text-sm text-muted-foreground">
          选择一个工具来提升您的工作效率
        </p>
      </div>

      <div className="grid gap-4 md:grid-cols-1 lg:grid-cols-2">
        {TOOLBOX_TOOLS.map((tool) => {
          const IconComponent = tool.icon
          const isActive = tool.status === "active"

          return (
            <Card
              key={tool.id}
              className={`group cursor-pointer transition-all duration-300 hover:shadow-lg hover:scale-[1.02] ${
                isActive
                  ? "hover:border-primary hover:shadow-primary/20 border-2"
                  : "opacity-60 cursor-not-allowed hover:scale-100"
              }`}
              onClick={() => handleToolSelect(tool.id)}
            >
              <CardHeader className="pb-3">
                <div className="flex items-start justify-between">
                  <div className="flex items-center gap-3">
                    <div className={`p-3 rounded-xl transition-all duration-300 ${
                      isActive
                        ? "bg-gradient-to-br from-primary/10 to-primary/5 text-primary shadow-sm"
                        : "bg-muted text-muted-foreground"
                    }`}>
                      <IconComponent className={`h-6 w-6 transition-transform duration-300 ${
                        isActive ? "group-hover:scale-110" : ""
                      }`} />
                    </div>
                    <div>
                      <CardTitle className="text-base">{tool.name}</CardTitle>
                      <Badge
                        variant={isActive ? "default" : "secondary"}
                        className="text-xs mt-1"
                      >
                        {tool.category}
                      </Badge>
                    </div>
                  </div>
                  {!isActive && (
                    <Badge variant="outline" className="text-xs">
                      即将推出
                    </Badge>
                  )}
                </div>
              </CardHeader>
              <CardContent className="pt-0">
                <CardDescription className="text-sm mb-3">
                  {tool.description}
                </CardDescription>
                <div className="flex flex-wrap gap-1">
                  {tool.features.map((feature, index) => (
                    <Badge
                      key={index}
                      variant="outline"
                      className="text-xs"
                    >
                      {feature}
                    </Badge>
                  ))}
                </div>
              </CardContent>
            </Card>
          )
        })}
      </div>

      <div className="text-center text-sm text-muted-foreground mt-6">
        更多工具正在开发中，敬请期待...
      </div>
    </div>
  )

  const renderSelectedTool = () => {
    switch (selectedTool) {
      case "website-tool-extractor":
        return <WebsiteToolExtractor onBack={handleBack} />
      case "toolbox-analytics":
        return <ToolboxStatsPanel />
      case "toolbox-help":
        return <ToolboxHelp />
      default:
        return renderToolList()
    }
  }

  return (
    <Dialog open={isOpen} onOpenChange={setIsOpen}>
      <DialogTrigger asChild>
        <Button variant="ghost" size="sm">
          <Wrench className="h-4 w-4 mr-2" />
          <span className="hidden sm:inline">工具箱</span>
        </Button>
      </DialogTrigger>
      <DialogContent className="max-w-4xl max-h-[80vh] overflow-y-auto">
        <DialogHeader className={selectedTool ? "border-b pb-4" : ""}>
          {selectedTool ? (
            <div className="flex items-center gap-3">
              <Button
                variant="ghost"
                size="sm"
                onClick={handleBack}
                className="p-2"
              >
                <ArrowLeft className="h-4 w-4" />
              </Button>
              <div>
                <DialogTitle>
                  {TOOLBOX_TOOLS.find(t => t.id === selectedTool)?.name}
                </DialogTitle>
                <DialogDescription>
                  {TOOLBOX_TOOLS.find(t => t.id === selectedTool)?.description}
                </DialogDescription>
              </div>
            </div>
          ) : (
            <>
              <DialogTitle className="flex items-center gap-2">
                <Wrench className="h-5 w-5" />
                实用工具箱
              </DialogTitle>
              <DialogDescription>
                提供各种实用工具来提升您的工作效率
              </DialogDescription>
            </>
          )}
        </DialogHeader>

        <div className="py-4">
          {renderSelectedTool()}
        </div>
      </DialogContent>
    </Dialog>
  )
}
