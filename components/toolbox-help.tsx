"use client"

import React from "react"
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Alert, AlertDescription } from "@/components/ui/alert"
import { Separator } from "@/components/ui/separator"
import { 
  HelpCircle, 
  Globe, 
  Lightbulb, 
  CheckCircle,
  AlertTriangle,
  ExternalLink,
  Copy,
  Download,
  Upload
} from "lucide-react"

interface ToolboxHelpProps {
  className?: string
}

export function ToolboxHelp({ className }: ToolboxHelpProps) {
  return (
    <Card className={className}>
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <HelpCircle className="h-5 w-5" />
          工具箱使用指南
        </CardTitle>
        <CardDescription>
          详细的使用说明和最佳实践，帮助您更好地使用工具箱功能
        </CardDescription>
      </CardHeader>
      <CardContent className="space-y-6">
        {/* 网站工具提取指南 */}
        <div>
          <h4 className="text-sm font-medium mb-3 flex items-center gap-2">
            <Globe className="h-4 w-4" />
            网站工具提取使用指南
          </h4>
          
          <div className="space-y-4">
            <div>
              <h5 className="text-sm font-medium mb-2">✅ 推荐的网站类型</h5>
              <div className="space-y-2 text-sm text-muted-foreground">
                <div className="flex items-center gap-2">
                  <CheckCircle className="h-4 w-4 text-green-500" />
                  <span>工具导航网站（如工具大全、资源导航）</span>
                </div>
                <div className="flex items-center gap-2">
                  <CheckCircle className="h-4 w-4 text-green-500" />
                  <span>开发者资源网站（如 GitHub Awesome 列表）</span>
                </div>
                <div className="flex items-center gap-2">
                  <CheckCircle className="h-4 w-4 text-green-500" />
                  <span>设计师工具集合网站</span>
                </div>
                <div className="flex items-center gap-2">
                  <CheckCircle className="h-4 w-4 text-green-500" />
                  <span>在线工具聚合平台</span>
                </div>
              </div>
            </div>

            <div>
              <h5 className="text-sm font-medium mb-2">⚠️ 不适合的网站类型</h5>
              <div className="space-y-2 text-sm text-muted-foreground">
                <div className="flex items-center gap-2">
                  <AlertTriangle className="h-4 w-4 text-yellow-500" />
                  <span>社交媒体网站（Twitter、Facebook等）</span>
                </div>
                <div className="flex items-center gap-2">
                  <AlertTriangle className="h-4 w-4 text-yellow-500" />
                  <span>电商购物网站</span>
                </div>
                <div className="flex items-center gap-2">
                  <AlertTriangle className="h-4 w-4 text-yellow-500" />
                  <span>纯文章/博客网站</span>
                </div>
                <div className="flex items-center gap-2">
                  <AlertTriangle className="h-4 w-4 text-yellow-500" />
                  <span>需要JavaScript渲染的动态网站</span>
                </div>
              </div>
            </div>
          </div>
        </div>

        <Separator />

        {/* 使用步骤 */}
        <div>
          <h4 className="text-sm font-medium mb-3 flex items-center gap-2">
            <Lightbulb className="h-4 w-4" />
            使用步骤
          </h4>
          
          <div className="space-y-3">
            <div className="flex gap-3">
              <Badge variant="outline" className="min-w-[24px] h-6 flex items-center justify-center">1</Badge>
              <div className="flex-1">
                <div className="text-sm font-medium">输入网站URL</div>
                <div className="text-xs text-muted-foreground">
                  输入要分析的工具网站地址，确保URL格式正确
                </div>
              </div>
            </div>
            
            <div className="flex gap-3">
              <Badge variant="outline" className="min-w-[24px] h-6 flex items-center justify-center">2</Badge>
              <div className="flex-1">
                <div className="text-sm font-medium">开始分析</div>
                <div className="text-xs text-muted-foreground">
                  点击"开始分析"按钮，AI将智能识别网站中的工具链接
                </div>
              </div>
            </div>
            
            <div className="flex gap-3">
              <Badge variant="outline" className="min-w-[24px] h-6 flex items-center justify-center">3</Badge>
              <div className="flex-1">
                <div className="text-sm font-medium">查看结果</div>
                <div className="text-xs text-muted-foreground">
                  查看提取的工具列表，按置信度排序显示
                </div>
              </div>
            </div>
            
            <div className="flex gap-3">
              <Badge variant="outline" className="min-w-[24px] h-6 flex items-center justify-center">4</Badge>
              <div className="flex-1">
                <div className="text-sm font-medium">选择和导出</div>
                <div className="text-xs text-muted-foreground">
                  选择需要的工具，支持复制、导出或直接导入
                </div>
              </div>
            </div>
          </div>
        </div>

        <Separator />

        {/* 功能说明 */}
        <div>
          <h4 className="text-sm font-medium mb-3">功能说明</h4>
          
          <div className="space-y-3">
            <div className="flex items-start gap-3">
              <Copy className="h-4 w-4 mt-0.5 text-blue-500" />
              <div>
                <div className="text-sm font-medium">复制链接</div>
                <div className="text-xs text-muted-foreground">
                  将选中的工具URL复制到剪贴板，方便手动添加
                </div>
              </div>
            </div>
            
            <div className="flex items-start gap-3">
              <Download className="h-4 w-4 mt-0.5 text-green-500" />
              <div>
                <div className="text-sm font-medium">导出TXT</div>
                <div className="text-xs text-muted-foreground">
                  将选中的工具URL导出为文本文件，便于保存和分享
                </div>
              </div>
            </div>
            
            <div className="flex items-start gap-3">
              <Upload className="h-4 w-4 mt-0.5 text-purple-500" />
              <div>
                <div className="text-sm font-medium">直接导入</div>
                <div className="text-xs text-muted-foreground">
                  将选中的工具直接导入到批量导入功能中，一键添加到工具库
                </div>
              </div>
            </div>
          </div>
        </div>

        <Separator />

        {/* 最佳实践 */}
        <div>
          <h4 className="text-sm font-medium mb-3">最佳实践</h4>
          
          <Alert>
            <Lightbulb className="h-4 w-4" />
            <AlertDescription>
              <div className="space-y-2">
                <div><strong>提高成功率：</strong></div>
                <ul className="text-xs space-y-1 ml-4">
                  <li>• 选择专门的工具导航网站</li>
                  <li>• 避免分析过于复杂的动态网站</li>
                  <li>• 优先选择静态HTML页面</li>
                  <li>• 检查网站是否可以正常访问</li>
                </ul>
                
                <div className="mt-3"><strong>优化体验：</strong></div>
                <ul className="text-xs space-y-1 ml-4">
                  <li>• 重复分析同一网站会使用缓存，速度更快</li>
                  <li>• 优先选择置信度高的工具</li>
                  <li>• 使用直接导入功能提高效率</li>
                  <li>• 定期查看使用统计了解使用情况</li>
                </ul>
              </div>
            </AlertDescription>
          </Alert>
        </div>

        <Separator />

        {/* 示例网站 */}
        <div>
          <h4 className="text-sm font-medium mb-3">推荐的示例网站</h4>
          
          <div className="space-y-2">
            <div className="flex items-center justify-between p-2 bg-muted/50 rounded">
              <div>
                <div className="text-sm font-medium">GitHub Awesome Lists</div>
                <div className="text-xs text-muted-foreground">开发者工具和资源集合</div>
              </div>
              <ExternalLink className="h-4 w-4 text-muted-foreground" />
            </div>
            
            <div className="flex items-center justify-between p-2 bg-muted/50 rounded">
              <div>
                <div className="text-sm font-medium">工具导航网站</div>
                <div className="text-xs text-muted-foreground">各类在线工具集合</div>
              </div>
              <ExternalLink className="h-4 w-4 text-muted-foreground" />
            </div>
            
            <div className="flex items-center justify-between p-2 bg-muted/50 rounded">
              <div>
                <div className="text-sm font-medium">设计师资源网站</div>
                <div className="text-xs text-muted-foreground">设计工具和素材集合</div>
              </div>
              <ExternalLink className="h-4 w-4 text-muted-foreground" />
            </div>
          </div>
        </div>
      </CardContent>
    </Card>
  )
}
