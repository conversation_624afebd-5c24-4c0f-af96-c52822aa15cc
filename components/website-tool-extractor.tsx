"use client"

import React, { useState } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Textarea } from "@/components/ui/textarea"
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Alert, AlertDescription } from "@/components/ui/alert"
import { Progress } from "@/components/ui/progress"
import { Separator } from "@/components/ui/separator"
import {
  Loader2,
  Globe,
  Copy,
  Download,
  ExternalLink,
  CheckCircle,
  AlertCircle,
  Info,
  ArrowLeft
} from "lucide-react"
import { useToast } from "@/hooks/use-toast"
import { ToolboxAnalytics } from "@/lib/toolbox-analytics"

interface ExtractedTool {
  url: string
  title?: string
  description?: string
  category?: string
  confidence: number
}

interface ExtractionResult {
  sourceUrl: string
  extractedTools: ExtractedTool[]
  totalCount: number
  processingTime: number
  summary?: {
    totalFound: number
    highConfidence: number
    categories: string[]
  }
}

interface WebsiteToolExtractorProps {
  onBack: () => void
}

export function WebsiteToolExtractor({ onBack }: WebsiteToolExtractorProps) {
  const { toast } = useToast()
  const [url, setUrl] = useState("")
  const [isAnalyzing, setIsAnalyzing] = useState(false)
  const [progress, setProgress] = useState(0)
  const [currentStage, setCurrentStage] = useState("")
  const [result, setResult] = useState<ExtractionResult | null>(null)
  const [error, setError] = useState<string | null>(null)
  const [selectedTools, setSelectedTools] = useState<Set<string>>(new Set())
  const [performanceMetrics, setPerformanceMetrics] = useState<{
    memoryUsage?: number
    renderTime?: number
  }>({})

  // 记录工具打开事件
  React.useEffect(() => {
    ToolboxAnalytics.logEvent({
      toolId: 'website-tool-extractor',
      action: 'open',
      success: true
    })
  }, [])

  // 性能监控
  React.useEffect(() => {
    const updatePerformanceMetrics = () => {
      if ('memory' in performance) {
        const memory = (performance as any).memory
        setPerformanceMetrics(prev => ({
          ...prev,
          memoryUsage: Math.round(memory.usedJSHeapSize / 1024 / 1024) // MB
        }))
      }
    }

    updatePerformanceMetrics()
    const interval = setInterval(updatePerformanceMetrics, 5000)

    return () => clearInterval(interval)
  }, [])

  const validateUrl = (url: string): boolean => {
    try {
      new URL(url)
      return true
    } catch {
      return false
    }
  }

  const handleAnalyze = async () => {
    if (!url.trim()) {
      toast({
        title: "请输入URL",
        description: "请输入要分析的网站地址",
        variant: "destructive"
      })
      return
    }

    if (!validateUrl(url)) {
      toast({
        title: "URL格式错误",
        description: "请输入有效的网站地址，如：https://example.com",
        variant: "destructive"
      })
      return
    }

    setIsAnalyzing(true)
    setProgress(0)
    setError(null)
    setResult(null)
    setSelectedTools(new Set())

    const startTime = Date.now()

    try {
      // 阶段1：检查缓存
      setCurrentStage("检查缓存...")
      setProgress(10)
      await new Promise(resolve => setTimeout(resolve, 200))

      // 阶段2：获取网页内容
      setCurrentStage("正在获取网页内容...")
      setProgress(25)

      const response = await fetch('/api/extract-tools', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ url: url.trim() })
      })

      // 阶段3：分析网页结构
      setCurrentStage("正在分析网页结构...")
      setProgress(50)

      if (!response.ok) {
        const errorData = await response.json()
        throw new Error(errorData.error || '分析失败')
      }

      // 阶段4：AI智能识别
      setCurrentStage("AI正在智能识别工具...")
      setProgress(75)

      const data = await response.json()

      // 阶段5：结果处理
      setCurrentStage("正在处理和验证结果...")
      setProgress(90)
      await new Promise(resolve => setTimeout(resolve, 300))

      setCurrentStage("分析完成")
      setProgress(100)

      if (data.success) {
        setResult(data.data)
        // 默认选中置信度高的工具
        const highConfidenceTools = new Set(
          data.data.extractedTools
            .filter((tool: ExtractedTool) => tool.confidence > 0.8)
            .map((tool: ExtractedTool) => tool.url)
        )
        setSelectedTools(highConfidenceTools)

        const cacheInfo = data.data.cached ? "（使用缓存结果）" : ""
        toast({
          title: "分析完成",
          description: `成功提取 ${data.data.totalCount} 个工具链接${cacheInfo}`,
        })

        // 记录成功的分析事件
        ToolboxAnalytics.logEvent({
          toolId: 'website-tool-extractor',
          action: 'analyze',
          url: url.trim(),
          resultCount: data.data.totalCount,
          processingTime: Date.now() - startTime,
          success: true
        })
      } else {
        throw new Error(data.error || '分析失败')
      }
    } catch (error) {
      console.error('工具提取失败:', error)
      setError(error instanceof Error ? error.message : '分析失败，请稍后重试')
      toast({
        title: "分析失败",
        description: error instanceof Error ? error.message : '请检查网址是否正确或稍后重试',
        variant: "destructive"
      })

      // 记录失败的分析事件
      ToolboxAnalytics.logEvent({
        toolId: 'website-tool-extractor',
        action: 'analyze',
        url: url.trim(),
        processingTime: Date.now() - startTime,
        success: false
      })
    } finally {
      setIsAnalyzing(false)
      setCurrentStage("")
      setProgress(0)
    }
  }

  const handleCopyUrl = (url: string) => {
    navigator.clipboard.writeText(url)
    toast({
      title: "已复制",
      description: "URL已复制到剪贴板",
    })
  }

  const handleCopySelected = () => {
    const selectedUrls = Array.from(selectedTools).join('\n')
    navigator.clipboard.writeText(selectedUrls)
    toast({
      title: "已复制",
      description: `已复制 ${selectedTools.size} 个URL到剪贴板`,
    })

    // 记录复制事件
    ToolboxAnalytics.logEvent({
      toolId: 'website-tool-extractor',
      action: 'copy',
      resultCount: selectedTools.size,
      success: true
    })
  }

  const handleExportTxt = () => {
    const selectedUrls = Array.from(selectedTools).join('\n')
    const blob = new Blob([selectedUrls], { type: 'text/plain' })
    const url = URL.createObjectURL(blob)
    const a = document.createElement('a')
    a.href = url
    a.download = `extracted-tools-${new Date().toISOString().split('T')[0]}.txt`
    document.body.appendChild(a)
    a.click()
    document.body.removeChild(a)
    URL.revokeObjectURL(url)

    toast({
      title: "导出成功",
      description: `已导出 ${selectedTools.size} 个URL到文件`,
    })

    // 记录导出事件
    ToolboxAnalytics.logEvent({
      toolId: 'website-tool-extractor',
      action: 'export',
      resultCount: selectedTools.size,
      success: true
    })
  }

  const handleDirectImport = async () => {
    const selectedUrls = Array.from(selectedTools).join('\n')

    try {
      // 显示开始导入的提示
      toast({
        title: "开始导入",
        description: `正在后台处理 ${selectedTools.size} 个工具链接...`,
      })

      // 直接调用批量导入服务
      const { BatchImportService } = await import('@/lib/cleanup-service')
      const taskId = await BatchImportService.processTextImport(
        selectedUrls,
        `工具箱提取导入: ${selectedTools.size}个工具`
      )

      // 显示任务创建成功提示
      toast({
        title: "导入任务已创建",
        description: `任务ID: ${taskId.substring(0, 8)}..., 正在后台处理，请在数据管理中查看进度`,
      })

      // 记录操作日志
      const { ExtendedDatabase } = await import('@/lib/database-extended')
      await ExtendedDatabase.createOperationLog(
        '工具箱直接导入',
        'import',
        'toolbox',
        undefined,
        {
          sourceUrl: url,
          toolCount: selectedTools.size,
          taskId: taskId,
          urls: Array.from(selectedTools)
        }
      )

      // 记录导入事件
      ToolboxAnalytics.logEvent({
        toolId: 'website-tool-extractor',
        action: 'import',
        resultCount: selectedTools.size,
        success: true
      })

      console.log('工具箱直接导入已启动:', {
        taskId,
        toolCount: selectedTools.size,
        sourceUrl: url
      })

    } catch (error) {
      console.error('直接导入失败:', error)
      toast({
        title: "导入失败",
        description: error instanceof Error ? error.message : "未知错误",
        variant: "destructive"
      })

      // 记录失败事件
      ToolboxAnalytics.logEvent({
        toolId: 'website-tool-extractor',
        action: 'import',
        resultCount: selectedTools.size,
        success: false
      })
    }
  }

  const handleSelectAll = () => {
    if (result) {
      const allUrls = new Set(result.extractedTools.map(tool => tool.url))
      setSelectedTools(allUrls)
    }
  }

  const handleSelectNone = () => {
    setSelectedTools(new Set())
  }

  const handleToolSelect = (url: string, selected: boolean) => {
    const newSelected = new Set(selectedTools)
    if (selected) {
      newSelected.add(url)
    } else {
      newSelected.delete(url)
    }
    setSelectedTools(newSelected)
  }

  const getConfidenceColor = (confidence: number) => {
    if (confidence >= 0.8) return "text-green-600"
    if (confidence >= 0.6) return "text-yellow-600"
    return "text-red-600"
  }

  const getConfidenceLabel = (confidence: number) => {
    if (confidence >= 0.8) return "高"
    if (confidence >= 0.6) return "中"
    return "低"
  }

  return (
    <div className="space-y-6">
      {/* 输入区域 */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Globe className="h-5 w-5" />
            网站工具提取
          </CardTitle>
          <CardDescription>
            输入工具导航网站或工具集合网站的URL，AI将自动分析并提取其中的工具链接
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="space-y-2">
            <Label htmlFor="url">网站地址</Label>
            <Input
              id="url"
              placeholder="https://example.com"
              value={url}
              onChange={(e) => setUrl(e.target.value)}
              disabled={isAnalyzing}
            />
          </div>

          <Button
            onClick={handleAnalyze}
            disabled={isAnalyzing || !url.trim()}
            className="w-full"
          >
            {isAnalyzing ? (
              <>
                <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                分析中...
              </>
            ) : (
              <>
                <Globe className="mr-2 h-4 w-4" />
                开始分析
              </>
            )}
          </Button>

          {/* 使用说明 */}
          <Alert>
            <Info className="h-4 w-4" />
            <AlertDescription>
              <strong>使用提示：</strong>
              <br />• 支持工具导航网站（如工具大全、资源导航等）
              <br />• 支持包含多个工具链接的页面
              <br />• AI会自动识别和过滤真正的工具链接
              <br />• 分析时间取决于网站大小，通常需要30-60秒
              <br />• 支持缓存机制，重复分析同一网站会更快
              <br />• 建议使用专门的工具导航网站以获得更好的效果
            </AlertDescription>
          </Alert>
        </CardContent>
      </Card>

      {/* 分析进度 */}
      {isAnalyzing && (
        <Card>
          <CardContent className="pt-6">
            <div className="space-y-3">
              <div className="flex items-center justify-between text-sm">
                <span>{currentStage}</span>
                <span>{progress}%</span>
              </div>
              <Progress value={progress} className="w-full" />
            </div>
          </CardContent>
        </Card>
      )}

      {/* 性能警告 */}
      {performanceMetrics.memoryUsage && performanceMetrics.memoryUsage > 100 && (
        <Alert>
          <AlertCircle className="h-4 w-4" />
          <AlertDescription>
            <strong>性能提示：</strong> 当前内存使用量较高 ({performanceMetrics.memoryUsage}MB)，
            建议在分析完成后刷新页面以释放内存。
          </AlertDescription>
        </Alert>
      )}

      {/* 错误信息 */}
      {error && (
        <Alert variant="destructive">
          <AlertCircle className="h-4 w-4" />
          <AlertDescription>{error}</AlertDescription>
        </Alert>
      )}

      {/* 分析结果 */}
      {result && (
        <div className="space-y-4">
          {/* 结果概览 */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <CheckCircle className="h-5 w-5 text-green-600" />
                分析结果
              </CardTitle>
              <CardDescription>
                从 {result.sourceUrl} 提取到 {result.totalCount} 个工具链接
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-center">
                <div>
                  <div className="text-2xl font-bold text-primary">{result.totalCount}</div>
                  <div className="text-sm text-muted-foreground">总数量</div>
                </div>
                <div>
                  <div className="text-2xl font-bold text-green-600">
                    {result.summary?.highConfidence || 0}
                  </div>
                  <div className="text-sm text-muted-foreground">高置信度</div>
                </div>
                <div>
                  <div className="text-2xl font-bold text-blue-600">{selectedTools.size}</div>
                  <div className="text-sm text-muted-foreground">已选择</div>
                </div>
                <div>
                  <div className="text-2xl font-bold text-purple-600">
                    {Math.round(result.processingTime / 1000)}s
                  </div>
                  <div className="text-sm text-muted-foreground">处理时间</div>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* 操作按钮 */}
          <Card>
            <CardContent className="pt-6">
              <div className="flex flex-wrap gap-2">
                <Button variant="outline" size="sm" onClick={handleSelectAll}>
                  全选
                </Button>
                <Button variant="outline" size="sm" onClick={handleSelectNone}>
                  取消全选
                </Button>
                <Button
                  variant="outline"
                  size="sm"
                  onClick={handleCopySelected}
                  disabled={selectedTools.size === 0}
                >
                  <Copy className="mr-2 h-4 w-4" />
                  复制选中 ({selectedTools.size})
                </Button>
                <Button
                  variant="outline"
                  size="sm"
                  onClick={handleExportTxt}
                  disabled={selectedTools.size === 0}
                >
                  <Download className="mr-2 h-4 w-4" />
                  导出TXT
                </Button>
                <Button
                  size="sm"
                  onClick={handleDirectImport}
                  disabled={selectedTools.size === 0}
                >
                  <Download className="mr-2 h-4 w-4" />
                  直接导入
                </Button>
              </div>
            </CardContent>
          </Card>

          {/* 工具列表 */}
          <Card>
            <CardHeader>
              <CardTitle>提取的工具列表</CardTitle>
              <CardDescription>
                点击复选框选择要导出的工具，置信度越高表示越可能是真正的工具链接
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-3 max-h-96 overflow-y-auto">
                {result.extractedTools.map((tool, index) => (
                  <div key={index} className="flex items-start gap-3 p-3 border rounded-lg">
                    <input
                      type="checkbox"
                      checked={selectedTools.has(tool.url)}
                      onChange={(e) => handleToolSelect(tool.url, e.target.checked)}
                      className="mt-1"
                    />
                    <div className="flex-1 min-w-0">
                      <div className="flex items-center gap-2 mb-1">
                        <span className="font-medium truncate">
                          {tool.title || tool.url}
                        </span>
                        <Badge
                          variant="outline"
                          className={`text-xs ${getConfidenceColor(tool.confidence)}`}
                        >
                          置信度: {getConfidenceLabel(tool.confidence)}
                        </Badge>
                        {tool.category && (
                          <Badge variant="secondary" className="text-xs">
                            {tool.category}
                          </Badge>
                        )}
                      </div>
                      {tool.description && (
                        <p className="text-sm text-muted-foreground mb-2">
                          {tool.description}
                        </p>
                      )}
                      <div className="flex items-center gap-2">
                        <code className="text-xs bg-muted px-2 py-1 rounded flex-1 truncate">
                          {tool.url}
                        </code>
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={() => handleCopyUrl(tool.url)}
                        >
                          <Copy className="h-3 w-3" />
                        </Button>
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={() => window.open(tool.url, '_blank')}
                        >
                          <ExternalLink className="h-3 w-3" />
                        </Button>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        </div>
      )}
    </div>
  )
}
