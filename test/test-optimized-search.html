<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>ToolMaster - 优化搜索功能测试</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            line-height: 1.6;
            background-color: #f8f9fa;
        }
        .container {
            background: white;
            border-radius: 8px;
            padding: 30px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .test-section {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 8px;
            padding: 20px;
            margin: 20px 0;
        }
        .test-section.success {
            background: #d4edda;
            border-color: #c3e6cb;
        }
        .test-section.error {
            background: #f8d7da;
            border-color: #f5c6cb;
        }
        .test-button {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
            font-size: 14px;
        }
        .test-button:hover {
            background: #0056b3;
        }
        .test-button:disabled {
            background: #6c757d;
            cursor: not-allowed;
        }
        .test-button.optimized {
            background: #28a745;
        }
        .test-button.optimized:hover {
            background: #218838;
        }
        .test-button.original {
            background: #ffc107;
            color: #212529;
        }
        .test-button.original:hover {
            background: #e0a800;
        }
        .input-group {
            margin: 15px 0;
        }
        .input-group label {
            display: block;
            margin-bottom: 5px;
            font-weight: 500;
        }
        .input-group input {
            width: 100%;
            padding: 8px 12px;
            border: 1px solid #ced4da;
            border-radius: 4px;
            font-size: 14px;
        }
        .result-display {
            background: #ffffff;
            border: 1px solid #dee2e6;
            border-radius: 4px;
            padding: 15px;
            margin: 10px 0;
            max-height: 500px;
            overflow-y: auto;
        }
        .performance-stats {
            background: #e3f2fd;
            border: 1px solid #bbdefb;
            border-radius: 4px;
            padding: 10px;
            margin: 10px 0;
            font-family: monospace;
            font-size: 12px;
        }
        .comparison-table {
            width: 100%;
            border-collapse: collapse;
            margin: 15px 0;
        }
        .comparison-table th,
        .comparison-table td {
            border: 1px solid #dee2e6;
            padding: 8px 12px;
            text-align: left;
        }
        .comparison-table th {
            background: #f8f9fa;
            font-weight: 600;
        }
        .status-indicator {
            display: inline-block;
            width: 12px;
            height: 12px;
            border-radius: 50%;
            margin-right: 8px;
        }
        .status-success { background-color: #28a745; }
        .status-error { background-color: #dc3545; }
        .status-pending { background-color: #ffc107; }
        .status-processing { background-color: #17a2b8; }
    </style>
</head>
<body>
    <div class="container">
        <h1>🚀 ToolMaster - 优化搜索功能测试</h1>
        <p>测试新的优化版搜索功能，对比性能提升效果。</p>

        <!-- 测试配置 -->
        <div class="test-section">
            <h2>🔧 测试配置</h2>
            
            <div class="input-group">
                <label for="apiKey">API 访问密钥:</label>
                <input type="password" id="apiKey" placeholder="输入您的API访问密钥">
            </div>

            <div class="input-group">
                <label for="searchQuery">搜索查询:</label>
                <input type="text" id="searchQuery" placeholder="输入搜索关键词" value="代码编辑器">
            </div>

            <div class="input-group">
                <label for="searchType">搜索类型:</label>
                <select id="searchType">
                    <option value="deep">深度搜索</option>
                    <option value="global">全网搜索</option>
                </select>
            </div>
        </div>

        <!-- 性能对比测试 -->
        <div class="test-section">
            <h2>⚡ 性能对比测试</h2>
            <p>同时测试优化版和原版搜索，对比性能差异。</p>
            
            <button class="test-button optimized" onclick="testOptimizedSearch()">
                <span id="optimizedButtonText">测试优化版搜索</span>
            </button>
            <button class="test-button original" onclick="testOriginalSearch()">
                <span id="originalButtonText">测试原版搜索</span>
            </button>
            <button class="test-button" onclick="testBothVersions()">
                <span id="bothButtonText">同时测试两个版本</span>
            </button>
            <button class="test-button" onclick="clearResults()">清空结果</button>

            <div id="performanceStats" class="performance-stats" style="display: none;">
                <h4>性能统计:</h4>
                <div id="performanceContent"></div>
            </div>
        </div>

        <!-- 测试结果显示 -->
        <div class="test-section">
            <h2>📊 测试结果</h2>
            
            <div id="optimizedResult" class="result-display" style="display: none;">
                <h4><span class="status-indicator status-success"></span>优化版搜索结果:</h4>
                <div id="optimizedResultContent"></div>
            </div>

            <div id="originalResult" class="result-display" style="display: none;">
                <h4><span class="status-indicator status-pending"></span>原版搜索结果:</h4>
                <div id="originalResultContent"></div>
            </div>

            <div id="comparisonResult" class="result-display" style="display: none;">
                <h4>📈 性能对比分析:</h4>
                <div id="comparisonContent"></div>
            </div>
        </div>

        <!-- 预设测试用例 -->
        <div class="test-section">
            <h2>📝 预设测试用例</h2>
            <p>使用预设的查询测试不同场景：</p>
            
            <h3>深度搜索测试:</h3>
            <button class="test-button" onclick="testPresetQuery('代码编辑器', 'deep')">代码编辑器</button>
            <button class="test-button" onclick="testPresetQuery('设计工具', 'deep')">设计工具</button>
            <button class="test-button" onclick="testPresetQuery('项目管理', 'deep')">项目管理</button>
            <button class="test-button" onclick="testPresetQuery('社区论坛', 'deep')">社区论坛</button>

            <h3>全网搜索测试:</h3>
            <button class="test-button" onclick="testPresetQuery('在线协作工具', 'global')">在线协作工具</button>
            <button class="test-button" onclick="testPresetQuery('AI写作助手', 'global')">AI写作助手</button>
            <button class="test-button" onclick="testPresetQuery('数据可视化', 'global')">数据可视化</button>
            <button class="test-button" onclick="testPresetQuery('视频编辑', 'global')">视频编辑</button>
        </div>

        <!-- 功能验证 -->
        <div class="test-section">
            <h2>✅ 功能验证</h2>
            <p>验证优化后的功能是否正常工作：</p>
            
            <table class="comparison-table">
                <thead>
                    <tr>
                        <th>功能项</th>
                        <th>优化版</th>
                        <th>原版</th>
                        <th>状态</th>
                    </tr>
                </thead>
                <tbody id="featureComparisonTable">
                    <tr>
                        <td>搜索响应时间</td>
                        <td id="optimizedTime">-</td>
                        <td id="originalTime">-</td>
                        <td id="timeStatus">未测试</td>
                    </tr>
                    <tr>
                        <td>结果准确性</td>
                        <td id="optimizedAccuracy">-</td>
                        <td id="originalAccuracy">-</td>
                        <td id="accuracyStatus">未测试</td>
                    </tr>
                    <tr>
                        <td>分类正确性</td>
                        <td id="optimizedCategory">-</td>
                        <td id="originalCategory">-</td>
                        <td id="categoryStatus">未测试</td>
                    </tr>
                    <tr>
                        <td>异步修正</td>
                        <td id="optimizedAsync">支持</td>
                        <td id="originalAsync">不支持</td>
                        <td id="asyncStatus">待验证</td>
                    </tr>
                </tbody>
            </table>
        </div>
    </div>

    <script>
        let testResults = {
            optimized: null,
            original: null
        };

        // 获取模拟工具数据（用于深度搜索测试）
        const mockTools = [
            { id: '1', name: 'VS Code', category: 'development', tags: ['编辑器', '代码', 'IDE'] },
            { id: '2', name: 'Figma', category: 'design', tags: ['设计', 'UI', '协作'] },
            { id: '3', name: 'Notion', category: 'productivity', tags: ['笔记', '文档', '协作'] },
            { id: '4', name: 'GitHub', category: 'development', tags: ['代码', 'Git', '版本控制'] },
            { id: '5', name: 'Trello', category: 'productivity', tags: ['项目管理', '看板', '任务'] }
        ];

        // 测试优化版搜索
        async function testOptimizedSearch() {
            const apiKey = document.getElementById('apiKey').value;
            const query = document.getElementById('searchQuery').value;
            const searchType = document.getElementById('searchType').value;

            if (!apiKey || !query) {
                alert('请输入API密钥和搜索查询');
                return;
            }

            const button = document.querySelector('.test-button.optimized');
            const buttonText = document.getElementById('optimizedButtonText');
            
            button.disabled = true;
            buttonText.innerHTML = '<span class="status-indicator status-processing"></span>测试中...';

            const startTime = Date.now();

            try {
                const endpoint = searchType === 'deep' ? '/api/deep-search' : '/api/global-search';
                const requestBody = searchType === 'deep' 
                    ? { query, tools: mockTools, useOptimized: true }
                    : { query, useOptimized: true };

                const response = await fetch(endpoint, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'X-API-Key': apiKey
                    },
                    body: JSON.stringify(requestBody)
                });

                const result = await response.json();
                const endTime = Date.now();
                const duration = endTime - startTime;

                testResults.optimized = { ...result, duration, searchType };

                // 显示结果
                displayOptimizedResult(result, duration);
                updatePerformanceStats();

            } catch (error) {
                console.error('优化版搜索测试失败:', error);
                displayError('optimizedResult', error.message);
            } finally {
                button.disabled = false;
                buttonText.innerHTML = '测试优化版搜索';
            }
        }

        // 测试原版搜索
        async function testOriginalSearch() {
            const apiKey = document.getElementById('apiKey').value;
            const query = document.getElementById('searchQuery').value;
            const searchType = document.getElementById('searchType').value;

            if (!apiKey || !query) {
                alert('请输入API密钥和搜索查询');
                return;
            }

            const button = document.querySelector('.test-button.original');
            const buttonText = document.getElementById('originalButtonText');
            
            button.disabled = true;
            buttonText.innerHTML = '<span class="status-indicator status-processing"></span>测试中...';

            const startTime = Date.now();

            try {
                const endpoint = searchType === 'deep' ? '/api/deep-search' : '/api/global-search';
                const requestBody = searchType === 'deep' 
                    ? { query, tools: mockTools, useOptimized: false }
                    : { query, useOptimized: false };

                const response = await fetch(endpoint, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'X-API-Key': apiKey
                    },
                    body: JSON.stringify(requestBody)
                });

                const result = await response.json();
                const endTime = Date.now();
                const duration = endTime - startTime;

                testResults.original = { ...result, duration, searchType };

                // 显示结果
                displayOriginalResult(result, duration);
                updatePerformanceStats();

            } catch (error) {
                console.error('原版搜索测试失败:', error);
                displayError('originalResult', error.message);
            } finally {
                button.disabled = false;
                buttonText.innerHTML = '测试原版搜索';
            }
        }

        // 同时测试两个版本
        async function testBothVersions() {
            const button = document.querySelector('button[onclick="testBothVersions()"]');
            const buttonText = document.getElementById('bothButtonText');
            
            button.disabled = true;
            buttonText.innerHTML = '<span class="status-indicator status-processing"></span>同时测试中...';

            try {
                // 并行执行两个测试
                await Promise.all([
                    testOptimizedSearch(),
                    testOriginalSearch()
                ]);

                // 显示对比结果
                displayComparisonResult();

            } catch (error) {
                console.error('并行测试失败:', error);
            } finally {
                button.disabled = false;
                buttonText.innerHTML = '同时测试两个版本';
            }
        }

        // 显示优化版结果
        function displayOptimizedResult(result, duration) {
            const resultDiv = document.getElementById('optimizedResult');
            const contentDiv = document.getElementById('optimizedResultContent');
            
            resultDiv.style.display = 'block';
            
            if (result.success) {
                contentDiv.innerHTML = `
                    <div style="color: #28a745;">
                        <p><strong>响应时间:</strong> ${duration}ms</p>
                        <p><strong>搜索总结:</strong> ${result.data.searchSummary}</p>
                        <p><strong>推荐工具数:</strong> ${result.data.recommendedTools?.length || 0}</p>
                        <p><strong>搜索洞察:</strong> ${result.data.searchInsights}</p>
                        ${result.data.status ? `<p><strong>状态:</strong> ${result.data.status}</p>` : ''}
                        ${result.data.searchId ? `<p><strong>搜索ID:</strong> ${result.data.searchId}</p>` : ''}
                        <details>
                            <summary>完整响应数据</summary>
                            <pre>${JSON.stringify(result, null, 2)}</pre>
                        </details>
                    </div>
                `;
            } else {
                displayError('optimizedResult', result.error || '未知错误');
            }
        }

        // 显示原版结果
        function displayOriginalResult(result, duration) {
            const resultDiv = document.getElementById('originalResult');
            const contentDiv = document.getElementById('originalResultContent');
            
            resultDiv.style.display = 'block';
            
            if (result.success) {
                contentDiv.innerHTML = `
                    <div style="color: #ffc107;">
                        <p><strong>响应时间:</strong> ${duration}ms</p>
                        <p><strong>搜索总结:</strong> ${result.data.searchSummary}</p>
                        <p><strong>推荐工具数:</strong> ${result.data.recommendedTools?.length || 0}</p>
                        <p><strong>搜索洞察:</strong> ${result.data.searchInsights}</p>
                        <details>
                            <summary>完整响应数据</summary>
                            <pre>${JSON.stringify(result, null, 2)}</pre>
                        </details>
                    </div>
                `;
            } else {
                displayError('originalResult', result.error || '未知错误');
            }
        }

        // 显示对比结果
        function displayComparisonResult() {
            if (!testResults.optimized || !testResults.original) {
                return;
            }

            const resultDiv = document.getElementById('comparisonResult');
            const contentDiv = document.getElementById('comparisonContent');
            
            resultDiv.style.display = 'block';

            const speedImprovement = testResults.original.duration - testResults.optimized.duration;
            const speedImprovementPercent = ((speedImprovement / testResults.original.duration) * 100).toFixed(1);

            contentDiv.innerHTML = `
                <table class="comparison-table">
                    <tr>
                        <th>指标</th>
                        <th>优化版</th>
                        <th>原版</th>
                        <th>改进</th>
                    </tr>
                    <tr>
                        <td>响应时间</td>
                        <td>${testResults.optimized.duration}ms</td>
                        <td>${testResults.original.duration}ms</td>
                        <td style="color: ${speedImprovement > 0 ? '#28a745' : '#dc3545'}">
                            ${speedImprovement > 0 ? '-' : '+'}${Math.abs(speedImprovement)}ms (${speedImprovementPercent}%)
                        </td>
                    </tr>
                    <tr>
                        <td>推荐工具数</td>
                        <td>${testResults.optimized.data?.recommendedTools?.length || 0}</td>
                        <td>${testResults.original.data?.recommendedTools?.length || 0}</td>
                        <td>-</td>
                    </tr>
                    <tr>
                        <td>异步处理</td>
                        <td>✅ 支持</td>
                        <td>❌ 不支持</td>
                        <td>✅ 新功能</td>
                    </tr>
                </table>
                
                <h4>性能分析:</h4>
                <ul>
                    <li><strong>速度提升:</strong> ${speedImprovement > 0 ? `优化版快了 ${speedImprovement}ms (${speedImprovementPercent}%)` : `优化版慢了 ${Math.abs(speedImprovement)}ms`}</li>
                    <li><strong>功能增强:</strong> 优化版支持异步分类修正</li>
                    <li><strong>用户体验:</strong> ${speedImprovement > 0 ? '显著提升' : '需要进一步优化'}</li>
                </ul>
            `;
        }

        // 更新性能统计
        function updatePerformanceStats() {
            const statsDiv = document.getElementById('performanceStats');
            const contentDiv = document.getElementById('performanceContent');
            
            statsDiv.style.display = 'block';

            let statsHtml = '<table class="comparison-table">';
            
            if (testResults.optimized) {
                statsHtml += `
                    <tr>
                        <td>优化版响应时间</td>
                        <td>${testResults.optimized.duration}ms</td>
                    </tr>
                `;
                document.getElementById('optimizedTime').textContent = `${testResults.optimized.duration}ms`;
            }
            
            if (testResults.original) {
                statsHtml += `
                    <tr>
                        <td>原版响应时间</td>
                        <td>${testResults.original.duration}ms</td>
                    </tr>
                `;
                document.getElementById('originalTime').textContent = `${testResults.original.duration}ms`;
            }

            if (testResults.optimized && testResults.original) {
                const improvement = testResults.original.duration - testResults.optimized.duration;
                const improvementPercent = ((improvement / testResults.original.duration) * 100).toFixed(1);
                
                statsHtml += `
                    <tr>
                        <td>性能提升</td>
                        <td style="color: ${improvement > 0 ? '#28a745' : '#dc3545'}">
                            ${improvement > 0 ? '+' : ''}${improvement}ms (${improvementPercent}%)
                        </td>
                    </tr>
                `;

                document.getElementById('timeStatus').textContent = improvement > 0 ? '✅ 提升' : '❌ 下降';
            }

            statsHtml += '</table>';
            contentDiv.innerHTML = statsHtml;
        }

        // 测试预设查询
        function testPresetQuery(query, type) {
            document.getElementById('searchQuery').value = query;
            document.getElementById('searchType').value = type;
            testBothVersions();
        }

        // 显示错误
        function displayError(containerId, errorMessage) {
            const resultDiv = document.getElementById(containerId);
            const contentDiv = document.getElementById(containerId + 'Content');
            
            resultDiv.style.display = 'block';
            resultDiv.className = 'result-display error';
            
            contentDiv.innerHTML = `
                <div style="color: #dc3545;">
                    <h5><span class="status-indicator status-error"></span>测试失败!</h5>
                    <p><strong>错误:</strong> ${errorMessage}</p>
                </div>
            `;
        }

        // 清空结果
        function clearResults() {
            document.getElementById('optimizedResult').style.display = 'none';
            document.getElementById('originalResult').style.display = 'none';
            document.getElementById('comparisonResult').style.display = 'none';
            document.getElementById('performanceStats').style.display = 'none';
            
            testResults = { optimized: null, original: null };
            
            // 重置表格
            document.getElementById('optimizedTime').textContent = '-';
            document.getElementById('originalTime').textContent = '-';
            document.getElementById('timeStatus').textContent = '未测试';
        }

        // 页面加载时的提示
        window.onload = function() {
            console.log('ToolMaster 优化搜索测试页面已加载');
            console.log('请确保您有有效的API访问密钥来测试功能');
        };
    </script>
</body>
</html>
