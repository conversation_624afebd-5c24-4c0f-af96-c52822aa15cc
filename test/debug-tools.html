<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>工具数据调试</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            line-height: 1.6;
        }
        .debug-section {
            margin: 20px 0;
            padding: 20px;
            border: 1px solid #ddd;
            border-radius: 8px;
        }
        .tool-item {
            background: #f8f9fa;
            padding: 10px;
            margin: 5px 0;
            border-radius: 4px;
            border-left: 3px solid #007bff;
        }
        .tool-name {
            font-weight: bold;
            color: #007bff;
        }
        .tool-desc {
            color: #666;
            font-size: 0.9em;
            margin: 5px 0;
        }
        .tool-tags {
            font-size: 0.8em;
            color: #28a745;
        }
        .search-box {
            width: 100%;
            padding: 10px;
            margin: 10px 0;
            border: 1px solid #ddd;
            border-radius: 4px;
        }
        .highlight {
            background-color: yellow;
            font-weight: bold;
        }
    </style>
</head>
<body>
    <h1>ToolMaster 工具数据调试</h1>
    
    <div class="debug-section">
        <h2>搜索工具</h2>
        <input type="text" id="searchInput" class="search-box" placeholder="搜索工具名称、描述或标签..." value="社区">
        <div id="searchResults"></div>
    </div>

    <div class="debug-section">
        <h2>所有工具列表</h2>
        <div>总数量: <span id="totalCount">加载中...</span></div>
        <div id="toolsList"></div>
    </div>

    <script>
        let allTools = [];

        // 获取工具数据
        async function loadTools() {
            try {
                // 尝试从localStorage获取
                const localTools = JSON.parse(localStorage.getItem('toolmaster-tools') || '[]');
                
                if (localTools.length > 0) {
                    allTools = localTools;
                    console.log('从localStorage加载了', allTools.length, '个工具');
                } else {
                    // 如果localStorage没有数据，尝试从API获取
                    console.log('localStorage没有数据，尝试从API获取...');
                    // 这里可以添加API调用逻辑
                }
                
                displayTools(allTools);
                updateTotalCount();
                performSearch();
            } catch (error) {
                console.error('加载工具数据失败:', error);
                document.getElementById('totalCount').textContent = '加载失败';
            }
        }

        // 显示工具列表
        function displayTools(tools) {
            const container = document.getElementById('toolsList');
            if (tools.length === 0) {
                container.innerHTML = '<p>没有找到工具数据。请确保：<br>1. 已经在主应用中添加了工具<br>2. 数据已保存到localStorage</p>';
                return;
            }

            container.innerHTML = tools.map(tool => `
                <div class="tool-item">
                    <div class="tool-name">${tool.name}</div>
                    <div class="tool-desc">${tool.description}</div>
                    <div class="tool-tags">标签: ${tool.tags.join(', ')}</div>
                    <div style="font-size: 0.8em; color: #666;">
                        分类: ${tool.category} > ${tool.subcategory} > ${tool.subsubcategory}
                    </div>
                    <div style="font-size: 0.8em; color: #666;">
                        URL: ${tool.url}
                    </div>
                </div>
            `).join('');
        }

        // 更新总数
        function updateTotalCount() {
            document.getElementById('totalCount').textContent = allTools.length;
        }

        // 搜索功能
        function performSearch() {
            const query = document.getElementById('searchInput').value.toLowerCase();
            const results = allTools.filter(tool => 
                tool.name.toLowerCase().includes(query) ||
                tool.description.toLowerCase().includes(query) ||
                tool.tags.some(tag => tag.toLowerCase().includes(query)) ||
                tool.url.toLowerCase().includes(query)
            );

            const container = document.getElementById('searchResults');
            if (query.trim() === '') {
                container.innerHTML = '<p>请输入搜索关键词</p>';
                return;
            }

            if (results.length === 0) {
                container.innerHTML = `<p>没有找到包含"${query}"的工具</p>`;
                return;
            }

            container.innerHTML = `
                <p>找到 ${results.length} 个相关工具：</p>
                ${results.map(tool => `
                    <div class="tool-item">
                        <div class="tool-name">${highlightText(tool.name, query)}</div>
                        <div class="tool-desc">${highlightText(tool.description, query)}</div>
                        <div class="tool-tags">标签: ${tool.tags.map(tag => highlightText(tag, query)).join(', ')}</div>
                    </div>
                `).join('')}
            `;
        }

        // 高亮搜索关键词
        function highlightText(text, query) {
            if (!query.trim()) return text;
            const regex = new RegExp(`(${query})`, 'gi');
            return text.replace(regex, '<span class="highlight">$1</span>');
        }

        // 事件监听
        document.getElementById('searchInput').addEventListener('input', performSearch);

        // 页面加载时执行
        loadTools();

        // 检查特定工具
        function checkSpecificTools() {
            const targetTools = ['天涯社区', '知乎', '豆瓣', '贴吧', '微博', '小红书'];
            console.log('检查特定工具:');
            targetTools.forEach(name => {
                const found = allTools.find(tool => 
                    tool.name.includes(name) || 
                    tool.description.includes(name) ||
                    tool.tags.some(tag => tag.includes(name))
                );
                console.log(`${name}: ${found ? '找到' : '未找到'}`, found ? found.name : '');
            });
        }

        // 延迟执行检查
        setTimeout(checkSpecificTools, 1000);
    </script>
</body>
</html>
