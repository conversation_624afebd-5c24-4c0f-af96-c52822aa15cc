<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>ToolMaster 分类结构验证</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            line-height: 1.6;
            background: #f8fafc;
        }
        .header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 30px;
            border-radius: 12px;
            margin-bottom: 30px;
            text-align: center;
        }
        .section {
            background: white;
            padding: 25px;
            margin-bottom: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .category {
            border: 1px solid #e2e8f0;
            border-radius: 8px;
            margin-bottom: 15px;
            overflow: hidden;
        }
        .category-header {
            background: #f1f5f9;
            padding: 15px;
            font-weight: bold;
            color: #1e293b;
            border-bottom: 1px solid #e2e8f0;
        }
        .subcategory {
            padding: 12px 20px;
            border-bottom: 1px solid #f1f5f9;
        }
        .subcategory:last-child {
            border-bottom: none;
        }
        .subcategory-name {
            font-weight: 600;
            color: #475569;
            margin-bottom: 8px;
        }
        .subsubcategories {
            display: flex;
            flex-wrap: wrap;
            gap: 8px;
        }
        .subsubcategory {
            background: #e0f2fe;
            color: #0369a1;
            padding: 4px 8px;
            border-radius: 4px;
            font-size: 0.85em;
        }
        .stats {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
            margin-bottom: 20px;
        }
        .stat-card {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            text-align: center;
        }
        .stat-number {
            font-size: 2em;
            font-weight: bold;
            color: #3b82f6;
        }
        .stat-label {
            color: #64748b;
            margin-top: 5px;
        }
        .success {
            color: #059669;
            font-weight: bold;
        }
        .error {
            color: #dc2626;
            font-weight: bold;
        }
        .test-button {
            background: #3b82f6;
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 6px;
            cursor: pointer;
            font-size: 16px;
            margin: 10px;
        }
        .test-button:hover {
            background: #2563eb;
        }
        .test-results {
            background: #f8fafc;
            border: 1px solid #e2e8f0;
            border-radius: 6px;
            padding: 15px;
            margin-top: 15px;
            font-family: monospace;
            white-space: pre-wrap;
        }
    </style>
</head>
<body>
    <div class="header">
        <h1>🔧 ToolMaster 分类结构验证</h1>
        <p>验证九大一级分类是否都有6个二级分类，每个二级分类是否都有4个三级分类</p>
    </div>

    <div class="stats" id="stats">
        <!-- 统计信息将在这里显示 -->
    </div>

    <div class="section">
        <h2>🧪 测试功能</h2>
        <button class="test-button" onclick="testCategoryStructure()">验证分类结构</button>
        <button class="test-button" onclick="testAIClassification()">测试AI分类</button>
        <button class="test-button" onclick="exportCategoryReport()">导出分类报告</button>
        <div id="testResults" class="test-results" style="display: none;"></div>
    </div>

    <div class="section">
        <h2>📊 分类结构详情</h2>
        <div id="categoryStructure">
            <!-- 分类结构将在这里显示 -->
        </div>
    </div>

    <script>
        // 预期的分类结构
        const expectedStructure = {
            totalCategories: 10,
            subcategoriesPerCategory: 6,
            subsubcategoriesPerSubcategory: 4
        };

        // 从localStorage获取分类数据
        function getCategories() {
            try {
                const categories = JSON.parse(localStorage.getItem('toolmaster-categories') || '[]');
                return categories;
            } catch (error) {
                console.error('获取分类数据失败:', error);
                return [];
            }
        }

        // 验证分类结构
        function testCategoryStructure() {
            const categories = getCategories();
            const results = [];
            let totalIssues = 0;

            results.push('🔍 开始验证分类结构...\n');

            // 验证一级分类数量
            if (categories.length === expectedStructure.totalCategories) {
                results.push(`✅ 一级分类数量正确: ${categories.length}个`);
            } else {
                results.push(`❌ 一级分类数量错误: 期望${expectedStructure.totalCategories}个，实际${categories.length}个`);
                totalIssues++;
            }

            // 验证每个一级分类的二级分类数量
            categories.forEach((category, index) => {
                const subcategoryCount = category.subcategories?.length || 0;
                if (subcategoryCount === expectedStructure.subcategoriesPerCategory) {
                    results.push(`✅ ${category.name}: ${subcategoryCount}个二级分类`);
                } else {
                    results.push(`❌ ${category.name}: 期望${expectedStructure.subcategoriesPerCategory}个二级分类，实际${subcategoryCount}个`);
                    totalIssues++;
                }

                // 验证每个二级分类的三级分类数量
                category.subcategories?.forEach((subcategory, subIndex) => {
                    const subsubcategoryCount = subcategory.subsubcategories?.length || 0;
                    if (subsubcategoryCount === expectedStructure.subsubcategoriesPerSubcategory) {
                        results.push(`  ✅ ${subcategory.name}: ${subsubcategoryCount}个三级分类`);
                    } else {
                        results.push(`  ❌ ${subcategory.name}: 期望${expectedStructure.subsubcategoriesPerSubcategory}个三级分类，实际${subsubcategoryCount}个`);
                        totalIssues++;
                    }
                });
            });

            results.push(`\n📊 验证完成！`);
            if (totalIssues === 0) {
                results.push(`🎉 所有分类结构都符合要求！`);
            } else {
                results.push(`⚠️ 发现 ${totalIssues} 个问题需要修复`);
            }

            document.getElementById('testResults').style.display = 'block';
            document.getElementById('testResults').textContent = results.join('\n');
        }

        // 测试AI分类功能
        function testAIClassification() {
            const results = [];
            results.push('🤖 测试AI分类功能...\n');
            
            // 这里可以添加AI分类测试逻辑
            results.push('ℹ️ AI分类测试需要在实际应用中进行');
            results.push('建议：');
            results.push('1. 打开ToolMaster主页');
            results.push('2. 尝试添加新工具');
            results.push('3. 使用AI自动分类功能');
            results.push('4. 验证分类结果是否正确');

            document.getElementById('testResults').style.display = 'block';
            document.getElementById('testResults').textContent = results.join('\n');
        }

        // 导出分类报告
        function exportCategoryReport() {
            const categories = getCategories();
            const report = {
                timestamp: new Date().toISOString(),
                totalCategories: categories.length,
                categories: categories.map(cat => ({
                    id: cat.id,
                    name: cat.name,
                    subcategoryCount: cat.subcategories?.length || 0,
                    subcategories: cat.subcategories?.map(sub => ({
                        id: sub.id,
                        name: sub.name,
                        subsubcategoryCount: sub.subsubcategories?.length || 0,
                        subsubcategories: sub.subsubcategories?.map(subsub => ({
                            id: subsub.id,
                            name: subsub.name
                        })) || []
                    })) || []
                }))
            };

            const blob = new Blob([JSON.stringify(report, null, 2)], { type: 'application/json' });
            const url = URL.createObjectURL(blob);
            const a = document.createElement('a');
            a.href = url;
            a.download = `toolmaster-category-report-${new Date().toISOString().split('T')[0]}.json`;
            document.body.appendChild(a);
            a.click();
            document.body.removeChild(a);
            URL.revokeObjectURL(url);

            document.getElementById('testResults').style.display = 'block';
            document.getElementById('testResults').textContent = '📄 分类报告已导出到下载文件夹';
        }

        // 显示统计信息
        function displayStats() {
            const categories = getCategories();
            let totalSubcategories = 0;
            let totalSubsubcategories = 0;

            categories.forEach(cat => {
                totalSubcategories += cat.subcategories?.length || 0;
                cat.subcategories?.forEach(sub => {
                    totalSubsubcategories += sub.subsubcategories?.length || 0;
                });
            });

            const statsHtml = `
                <div class="stat-card">
                    <div class="stat-number">${categories.length}</div>
                    <div class="stat-label">一级分类</div>
                </div>
                <div class="stat-card">
                    <div class="stat-number">${totalSubcategories}</div>
                    <div class="stat-label">二级分类</div>
                </div>
                <div class="stat-card">
                    <div class="stat-number">${totalSubsubcategories}</div>
                    <div class="stat-label">三级分类</div>
                </div>
                <div class="stat-card">
                    <div class="stat-number">${categories.length === 10 && totalSubcategories === 60 && totalSubsubcategories === 240 ? '✅' : '❌'}</div>
                    <div class="stat-label">结构完整性</div>
                </div>
            `;
            document.getElementById('stats').innerHTML = statsHtml;
        }

        // 显示分类结构
        function displayCategoryStructure() {
            const categories = getCategories();
            let html = '';

            categories.forEach(category => {
                html += `
                    <div class="category">
                        <div class="category-header">
                            ${category.name} (${category.id}) - ${category.subcategories?.length || 0}个二级分类
                        </div>
                `;

                category.subcategories?.forEach(subcategory => {
                    html += `
                        <div class="subcategory">
                            <div class="subcategory-name">
                                ${subcategory.name} (${subcategory.id}) - ${subcategory.subsubcategories?.length || 0}个三级分类
                            </div>
                            <div class="subsubcategories">
                    `;

                    subcategory.subsubcategories?.forEach(subsubcategory => {
                        html += `<span class="subsubcategory">${subsubcategory.name} (${subsubcategory.id})</span>`;
                    });

                    html += `
                            </div>
                        </div>
                    `;
                });

                html += `</div>`;
            });

            document.getElementById('categoryStructure').innerHTML = html;
        }

        // 页面加载时初始化
        document.addEventListener('DOMContentLoaded', function() {
            displayStats();
            displayCategoryStructure();
        });
    </script>
</body>
</html>
