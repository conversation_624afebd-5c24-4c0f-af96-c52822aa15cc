<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>ToolMaster - 添加工具API测试</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            line-height: 1.6;
            background-color: #f8f9fa;
        }
        .container {
            background: white;
            border-radius: 8px;
            padding: 30px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .test-section {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 8px;
            padding: 20px;
            margin: 20px 0;
        }
        .test-section.success {
            background: #d4edda;
            border-color: #c3e6cb;
        }
        .test-section.error {
            background: #f8d7da;
            border-color: #f5c6cb;
        }
        .test-button {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
            font-size: 14px;
        }
        .test-button:hover {
            background: #0056b3;
        }
        .test-button:disabled {
            background: #6c757d;
            cursor: not-allowed;
        }
        .code-block {
            background: #f8f9fa;
            border: 1px solid #e9ecef;
            border-radius: 4px;
            padding: 15px;
            font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
            font-size: 13px;
            overflow-x: auto;
            margin: 10px 0;
        }
        .input-group {
            margin: 15px 0;
        }
        .input-group label {
            display: block;
            margin-bottom: 5px;
            font-weight: 500;
        }
        .input-group input {
            width: 100%;
            padding: 8px 12px;
            border: 1px solid #ced4da;
            border-radius: 4px;
            font-size: 14px;
        }
        .result-display {
            background: #ffffff;
            border: 1px solid #dee2e6;
            border-radius: 4px;
            padding: 15px;
            margin: 10px 0;
            max-height: 400px;
            overflow-y: auto;
        }
        .curl-command {
            background: #2d3748;
            color: #e2e8f0;
            padding: 15px;
            border-radius: 4px;
            font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
            font-size: 13px;
            overflow-x: auto;
            margin: 10px 0;
            white-space: pre-wrap;
        }
        .status-indicator {
            display: inline-block;
            width: 12px;
            height: 12px;
            border-radius: 50%;
            margin-right: 8px;
        }
        .status-success { background-color: #28a745; }
        .status-error { background-color: #dc3545; }
        .status-pending { background-color: #ffc107; }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔧 ToolMaster - 添加工具API测试</h1>
        <p>这个页面用于测试新的 <code>/api/add-tool</code> 端点功能。</p>

        <!-- API信息展示 -->
        <div class="test-section">
            <h2>📋 API 信息</h2>
            <p><strong>端点:</strong> <code>POST /api/add-tool</code></p>
            <p><strong>认证:</strong> 需要在请求头中包含 <code>X-API-Key</code></p>
            <p><strong>功能:</strong> 自动分析URL并添加工具到数据库</p>
            
            <h3>请求格式:</h3>
            <div class="code-block">
{
  "url": "https://example.com"
}
            </div>

            <h3>响应格式:</h3>
            <div class="code-block">
{
  "success": true,
  "message": "工具添加成功",
  "tool": { ... },
  "analysis": { ... },
  "processingTime": 1234
}
            </div>
        </div>

        <!-- 测试表单 -->
        <div class="test-section">
            <h2>🧪 API 测试</h2>
            
            <div class="input-group">
                <label for="apiKey">API 访问密钥:</label>
                <input type="password" id="apiKey" placeholder="输入您的API访问密钥">
            </div>

            <div class="input-group">
                <label for="testUrl">测试URL:</label>
                <input type="url" id="testUrl" placeholder="https://example.com" value="https://github.com">
            </div>

            <button class="test-button" onclick="testAddToolAPI()">
                <span id="testButtonText">测试添加工具</span>
            </button>
            <button class="test-button" onclick="clearResults()">清空结果</button>

            <div id="testResult" class="result-display" style="display: none;">
                <h4>测试结果:</h4>
                <div id="testResultContent"></div>
            </div>
        </div>

        <!-- cURL 命令示例 -->
        <div class="test-section">
            <h2>💻 cURL 命令示例</h2>
            <p>您可以使用以下 cURL 命令来调用API：</p>
            
            <h3>基本用法:</h3>
            <div class="curl-command">curl -X POST "https://your-domain.com/api/add-tool" \
  -H "Content-Type: application/json" \
  -H "X-API-Key: YOUR_API_ACCESS_KEY" \
  -d '{"url": "https://github.com"}'</div>

            <h3>本地测试:</h3>
            <div class="curl-command">curl -X POST "http://localhost:3000/api/add-tool" \
  -H "Content-Type: application/json" \
  -H "X-API-Key: YOUR_API_ACCESS_KEY" \
  -d '{"url": "https://github.com"}'</div>

            <h3>使用 Postman:</h3>
            <div class="code-block">
URL: POST https://your-domain.com/api/add-tool
Headers:
  Content-Type: application/json
  X-API-Key: YOUR_API_ACCESS_KEY
Body (JSON):
{
  "url": "https://github.com"
}
            </div>
        </div>

        <!-- 预设测试用例 -->
        <div class="test-section">
            <h2>📝 预设测试用例</h2>
            <p>点击以下按钮测试不同类型的网站：</p>
            
            <button class="test-button" onclick="testPresetUrl('https://github.com')">GitHub</button>
            <button class="test-button" onclick="testPresetUrl('https://figma.com')">Figma</button>
            <button class="test-button" onclick="testPresetUrl('https://notion.so')">Notion</button>
            <button class="test-button" onclick="testPresetUrl('https://vercel.com')">Vercel</button>
            <button class="test-button" onclick="testPresetUrl('https://openai.com')">OpenAI</button>
        </div>

        <!-- 错误测试 -->
        <div class="test-section">
            <h2>⚠️ 错误情况测试</h2>
            <p>测试API的错误处理能力：</p>
            
            <button class="test-button" onclick="testErrorCase('invalid-url')">无效URL</button>
            <button class="test-button" onclick="testErrorCase('empty-url')">空URL</button>
            <button class="test-button" onclick="testErrorCase('wrong-key')">错误密钥</button>
            <button class="test-button" onclick="testErrorCase('no-key')">无密钥</button>
        </div>
    </div>

    <script>
        // 测试添加工具API
        async function testAddToolAPI() {
            const apiKey = document.getElementById('apiKey').value;
            const testUrl = document.getElementById('testUrl').value;
            const button = document.querySelector('.test-button');
            const buttonText = document.getElementById('testButtonText');
            const resultDiv = document.getElementById('testResult');
            const resultContent = document.getElementById('testResultContent');

            if (!apiKey) {
                alert('请输入API访问密钥');
                return;
            }

            if (!testUrl) {
                alert('请输入测试URL');
                return;
            }

            // 更新按钮状态
            button.disabled = true;
            buttonText.innerHTML = '<span class="status-indicator status-pending"></span>测试中...';

            try {
                const response = await fetch('/api/add-tool', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'X-API-Key': apiKey
                    },
                    body: JSON.stringify({ url: testUrl })
                });

                const result = await response.json();
                
                // 显示结果
                resultDiv.style.display = 'block';
                
                if (response.ok && result.success) {
                    resultContent.innerHTML = `
                        <div style="color: #28a745;">
                            <h5><span class="status-indicator status-success"></span>测试成功!</h5>
                            <p><strong>消息:</strong> ${result.message}</p>
                            <p><strong>处理时间:</strong> ${result.processingTime}ms</p>
                            <p><strong>工具名称:</strong> ${result.tool.name}</p>
                            <p><strong>工具描述:</strong> ${result.tool.description}</p>
                            <p><strong>分类:</strong> ${result.tool.category}/${result.tool.subcategory}/${result.tool.subsubcategory}</p>
                            <p><strong>标签:</strong> ${result.tool.tags.join(', ')}</p>
                            <details>
                                <summary>完整响应数据</summary>
                                <pre>${JSON.stringify(result, null, 2)}</pre>
                            </details>
                        </div>
                    `;
                } else {
                    resultContent.innerHTML = `
                        <div style="color: #dc3545;">
                            <h5><span class="status-indicator status-error"></span>测试失败!</h5>
                            <p><strong>错误:</strong> ${result.error || '未知错误'}</p>
                            <p><strong>错误代码:</strong> ${result.code || 'UNKNOWN'}</p>
                            <p><strong>HTTP状态:</strong> ${response.status}</p>
                            ${result.details ? `<p><strong>详细信息:</strong> ${result.details}</p>` : ''}
                            <details>
                                <summary>完整错误响应</summary>
                                <pre>${JSON.stringify(result, null, 2)}</pre>
                            </details>
                        </div>
                    `;
                }
            } catch (error) {
                resultDiv.style.display = 'block';
                resultContent.innerHTML = `
                    <div style="color: #dc3545;">
                        <h5><span class="status-indicator status-error"></span>网络错误!</h5>
                        <p><strong>错误:</strong> ${error.message}</p>
                        <p>请检查网络连接和服务器状态。</p>
                    </div>
                `;
            } finally {
                // 恢复按钮状态
                button.disabled = false;
                buttonText.innerHTML = '测试添加工具';
            }
        }

        // 测试预设URL
        function testPresetUrl(url) {
            document.getElementById('testUrl').value = url;
            testAddToolAPI();
        }

        // 测试错误情况
        async function testErrorCase(errorType) {
            const apiKey = document.getElementById('apiKey').value;
            let testData = {};
            let headers = { 'Content-Type': 'application/json' };

            switch (errorType) {
                case 'invalid-url':
                    testData = { url: 'not-a-valid-url' };
                    headers['X-API-Key'] = apiKey;
                    break;
                case 'empty-url':
                    testData = { url: '' };
                    headers['X-API-Key'] = apiKey;
                    break;
                case 'wrong-key':
                    testData = { url: 'https://github.com' };
                    headers['X-API-Key'] = 'wrong-api-key';
                    break;
                case 'no-key':
                    testData = { url: 'https://github.com' };
                    // 不添加API密钥
                    break;
            }

            try {
                const response = await fetch('/api/add-tool', {
                    method: 'POST',
                    headers: headers,
                    body: JSON.stringify(testData)
                });

                const result = await response.json();
                
                const resultDiv = document.getElementById('testResult');
                const resultContent = document.getElementById('testResultContent');
                
                resultDiv.style.display = 'block';
                resultContent.innerHTML = `
                    <div style="color: #dc3545;">
                        <h5><span class="status-indicator status-error"></span>错误测试: ${errorType}</h5>
                        <p><strong>HTTP状态:</strong> ${response.status}</p>
                        <p><strong>错误:</strong> ${result.error || '未知错误'}</p>
                        <p><strong>错误代码:</strong> ${result.code || 'UNKNOWN'}</p>
                        <details>
                            <summary>完整错误响应</summary>
                            <pre>${JSON.stringify(result, null, 2)}</pre>
                        </details>
                    </div>
                `;
            } catch (error) {
                console.error('Error testing error case:', error);
            }
        }

        // 清空结果
        function clearResults() {
            document.getElementById('testResult').style.display = 'none';
        }

        // 页面加载时的提示
        window.onload = function() {
            console.log('ToolMaster Add Tool API 测试页面已加载');
            console.log('请确保您有有效的API访问密钥来测试功能');
        };
    </script>
</body>
</html>
