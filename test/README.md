# 🧪 ToolMaster 测试目录

本目录包含 ToolMaster 项目的所有测试文件，包括HTML测试页面、测试脚本和测试数据。

## 📋 测试文件分类

### 🌐 HTML测试页面
- `test-add-tool-api.html` - 添加工具API测试页面
- `test-all-toast-fixes.html` - Toast修复测试
- `test-api-security.html` - API安全测试
- `test-backup-functionality.html` - 备份功能测试
- `test-bookmark-export.html` - 书签导出测试
- `test-button-styles.html` - 按钮样式测试
- `test-cache-persistence.html` - 缓存持久化测试
- `test-category-fix.html` - 分类修复测试
- `test-category-structure.html` - 分类结构测试
- `test-custom-toast.html` - 自定义Toast测试
- `test-deep-search.html` - 深度搜索测试
- `test-final-buttons.html` - 最终按钮测试
- `test-fixes.html` - 修复测试
- `test-global-search.html` - 全局搜索测试
- `test-improvements.html` - 改进测试
- `test-naming-consistency.html` - 命名一致性测试
- `test-ui-functionality.html` - UI功能测试
- `test-add-to-local.html` - 本地添加测试

### 🔧 调试工具
- `debug-tools.html` - 调试工具页面
- `layout-test.html` - 布局测试页面

### 📄 示例文件
- `sample-bookmarks-output.html` - 书签输出示例

### 📊 测试数据
- `test-backup-sample.json` - 备份测试样本数据

### 🔍 测试脚本
- `test-bookmark-validation.js` - 书签验证脚本
- `verify-naming-changes.js` - 命名变更验证脚本

## 🚀 使用说明

### HTML测试页面
1. 启动开发服务器：`pnpm dev`
2. 在浏览器中访问：`http://localhost:3000/test/文件名.html`
3. 按照页面说明进行测试

### 测试脚本
```bash
# 运行JavaScript测试脚本
node test/script-name.js
```

## 📝 测试规范

### 新增测试文件
所有新的测试文件都应该放在此目录下：
- HTML测试页面：以 `test-` 开头，`.html` 结尾
- 测试脚本：以 `test-` 开头，`.js` 结尾
- 测试数据：以 `test-` 开头，使用相应的文件扩展名

### 测试文件格式
- HTML文件应包含完整的测试说明
- 测试脚本应包含详细的注释
- 测试数据应使用标准格式（JSON、CSV等）

### 测试最佳实践
1. 每个测试文件应该专注于测试特定功能
2. 包含清晰的测试步骤和预期结果
3. 提供错误情况的测试用例
4. 定期清理过时的测试文件

---

**最后更新**: 2025-08-01  
**维护者**: ToolMaster 开发团队
