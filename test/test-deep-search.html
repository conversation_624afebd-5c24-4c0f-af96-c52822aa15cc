<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>深度搜索功能测试</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            line-height: 1.6;
        }
        .test-section {
            margin: 20px 0;
            padding: 20px;
            border: 1px solid #ddd;
            border-radius: 8px;
        }
        .test-input {
            width: 100%;
            padding: 10px;
            margin: 10px 0;
            border: 1px solid #ddd;
            border-radius: 4px;
        }
        .test-button {
            background: linear-gradient(to right, #3b82f6, #8b5cf6);
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
        }
        .test-button:hover {
            opacity: 0.9;
        }
        .test-button:disabled {
            opacity: 0.5;
            cursor: not-allowed;
        }
        .result {
            margin-top: 20px;
            padding: 15px;
            background: #f8f9fa;
            border-radius: 4px;
            white-space: pre-wrap;
        }
        .error {
            background: #fee;
            color: #c33;
        }
        .success {
            background: #efe;
            color: #363;
        }
    </style>
</head>
<body>
    <h1>ToolMaster 深度搜索功能测试</h1>

    <div class="test-section">
        <h2>测试说明</h2>
        <p>这个页面用于测试新添加的AI深度搜索功能。请确保：</p>
        <ul>
            <li>Next.js 开发服务器正在运行 (http://localhost:3001)</li>
            <li>DeepSeek API 密钥配置正确</li>
            <li>有足够的测试数据</li>
        </ul>
    </div>

    <div class="test-section">
        <h2>深度搜索测试</h2>
        <input type="text" id="searchQuery" class="test-input"
               placeholder="输入搜索查询，例如：好玩的社区论坛"
               value="好玩的社区论坛">
        <br>
        <button id="testDeepSearch" class="test-button">执行深度搜索</button>
        <div id="deepSearchResult" class="result" style="display: none;"></div>
    </div>

    <div class="test-section">
        <h2>修复验证</h2>
        <p><strong>已修复的问题：</strong></p>
        <ul>
            <li>✅ 访问页面标题从"ToolShift"改为"ToolMaster"</li>
            <li>✅ "AI深度搜索"改为"深度搜索"</li>
            <li>✅ 修复了undefined错误，添加了安全检查</li>
            <li>✅ 改进了错误处理和数据验证</li>
        </ul>
    </div>

    <div class="test-section">
        <h2>测试用例</h2>
        <p>建议测试的查询：</p>
        <ul>
            <li><strong>好玩的社区论坛</strong> - 应该推荐知乎、豆瓣、天涯社区、百度贴吧</li>
            <li><strong>有名的社区</strong> - 应该推荐知乎、豆瓣、天涯社区等</li>
            <li><strong>有名的论坛</strong> - 应该推荐天涯社区、百度贴吧等</li>
            <li><strong>社交平台</strong> - 应该推荐微博、小红书等</li>
            <li><strong>最好用的在线电影观看网站</strong> - 应该推荐腾讯视频</li>
        </ul>
    </div>

    <script>
        // 模拟工具数据 - 包含社区论坛类工具
        const mockTools = [
            {
                id: "1",
                name: "知乎",
                url: "https://www.zhihu.com",
                description: "知乎是中文互联网高质量的问答社区和创作者聚集的原创内容平台",
                tags: ["问答", "社区", "知识分享", "讨论"],
                category: "entertainment",
                subcategory: "social-entertainment",
                subsubcategory: "community",
                addedAt: "2024-01-01T00:00:00Z",
                sensitive: false
            },
            {
                id: "2",
                name: "豆瓣",
                url: "https://www.douban.com",
                description: "豆瓣是一个文化产品的评价网站，也是一个文化社区",
                tags: ["电影", "读书", "音乐", "社区", "评分"],
                category: "entertainment",
                subcategory: "social-entertainment",
                subsubcategory: "community",
                addedAt: "2024-01-01T00:00:00Z",
                sensitive: false
            },
            {
                id: "3",
                name: "天涯社区",
                url: "https://www.tianya.cn",
                description: "天涯社区是中国最具影响力的网络社区之一，汇聚各类话题讨论",
                tags: ["论坛", "社区", "讨论", "话题"],
                category: "entertainment",
                subcategory: "social-entertainment",
                subsubcategory: "community",
                addedAt: "2024-01-01T00:00:00Z",
                sensitive: false
            },
            {
                id: "4",
                name: "百度贴吧",
                url: "https://tieba.baidu.com",
                description: "百度贴吧是中国最大的中文社区，基于关键词的主题交流社区",
                tags: ["贴吧", "社区", "讨论", "兴趣"],
                category: "entertainment",
                subcategory: "social-entertainment",
                subsubcategory: "community",
                addedAt: "2024-01-01T00:00:00Z",
                sensitive: false
            },
            {
                id: "5",
                name: "微博",
                url: "https://weibo.com",
                description: "新浪微博是中国最大的社交媒体平台，提供信息分享和社交功能",
                tags: ["微博", "社交", "分享", "热点"],
                category: "entertainment",
                subcategory: "social-entertainment",
                subsubcategory: "social-media",
                addedAt: "2024-01-01T00:00:00Z",
                sensitive: false
            },
            {
                id: "6",
                name: "小红书",
                url: "https://www.xiaohongshu.com",
                description: "小红书是年轻人的生活方式平台和消费决策入口",
                tags: ["生活", "分享", "购物", "社区"],
                category: "entertainment",
                subcategory: "social-entertainment",
                subsubcategory: "social-media",
                addedAt: "2024-01-01T00:00:00Z",
                sensitive: false
            },
            {
                id: "7",
                name: "腾讯视频",
                url: "https://v.qq.com",
                description: "腾讯视频是中国领先的在线视频平台，提供丰富的影视内容",
                tags: ["视频", "电影", "电视剧", "在线观看"],
                category: "entertainment",
                subcategory: "media-streaming",
                subsubcategory: "video-streaming",
                addedAt: "2024-01-01T00:00:00Z",
                sensitive: false
            },
            {
                id: "8",
                name: "Adobe Acrobat",
                url: "https://acrobat.adobe.com",
                description: "Adobe Acrobat是专业的PDF创建、编辑和管理工具",
                tags: ["PDF", "文档", "编辑", "专业"],
                category: "productivity",
                subcategory: "office-tools",
                subsubcategory: "pdf-tools",
                addedAt: "2024-01-01T00:00:00Z",
                sensitive: false
            }
        ];

        document.getElementById('testDeepSearch').addEventListener('click', async () => {
            const query = document.getElementById('searchQuery').value;
            const button = document.getElementById('testDeepSearch');
            const resultDiv = document.getElementById('deepSearchResult');

            if (!query.trim()) {
                alert('请输入搜索查询');
                return;
            }

            button.disabled = true;
            button.textContent = '搜索中...';
            resultDiv.style.display = 'block';
            resultDiv.className = 'result';
            resultDiv.textContent = '正在执行深度搜索...';

            try {
                const response = await fetch('http://localhost:3001/api/deep-search', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        query: query,
                        tools: mockTools
                    })
                });

                const data = await response.json();

                if (response.ok) {
                    resultDiv.className = 'result success';

                    // 格式化显示结果
                    let displayText = `✅ 深度搜索成功！\n\n`;
                    if (data.data && data.data.recommendedTools) {
                        displayText += `📊 搜索理解: ${data.data.searchSummary || '无'}\n`;
                        displayText += `💡 分析洞察: ${data.data.searchInsights || '无'}\n\n`;
                        displayText += `🎯 推荐工具 (${data.data.recommendedTools.length}个):\n`;

                        data.data.recommendedTools.forEach((tool, index) => {
                            displayText += `\n${index + 1}. ID: ${tool.id}\n`;
                            displayText += `   相关性: ${Math.round(tool.relevanceScore * 100)}%\n`;
                            displayText += `   推荐理由: ${tool.recommendReason}\n`;
                        });
                    } else {
                        displayText += `原始返回数据:\n${JSON.stringify(data, null, 2)}`;
                    }

                    resultDiv.textContent = displayText;
                } else {
                    resultDiv.className = 'result error';
                    resultDiv.textContent = `错误: ${data.error}\n详情: ${data.details || '无'}`;
                }
            } catch (error) {
                resultDiv.className = 'result error';
                resultDiv.textContent = `请求失败: ${error.message}`;
            } finally {
                button.disabled = false;
                button.textContent = '执行深度搜索';
            }
        });
    </script>
</body>
</html>
