<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>数据导出页面布局优化</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            line-height: 1.6;
            background: #f8f9fa;
        }
        .container {
            background: white;
            border-radius: 12px;
            padding: 24px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        }
        .header {
            text-align: center;
            margin-bottom: 30px;
            padding-bottom: 20px;
            border-bottom: 2px solid #e9ecef;
        }
        .grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 24px;
            margin-bottom: 24px;
        }
        @media (max-width: 1024px) {
            .grid {
                grid-template-columns: 1fr;
            }
        }
        .card {
            background: #fff;
            border: 1px solid #e9ecef;
            border-radius: 8px;
            overflow: hidden;
            box-shadow: 0 1px 3px rgba(0,0,0,0.1);
            height: fit-content;
        }
        .card-header {
            background: #f8f9fa;
            padding: 16px;
            border-bottom: 1px solid #e9ecef;
        }
        .card-title {
            margin: 0;
            font-size: 16px;
            font-weight: 600;
            color: #212529;
            display: flex;
            align-items: center;
            gap: 8px;
        }
        .card-description {
            margin: 4px 0 0 0;
            font-size: 14px;
            color: #6c757d;
        }
        .card-content {
            padding: 16px;
        }
        .info-box {
            padding: 12px;
            border-radius: 6px;
            margin-bottom: 16px;
            font-size: 14px;
        }
        .info-box.blue {
            background: #e3f2fd;
            border: 1px solid #bbdefb;
            color: #1565c0;
        }
        .info-box.green {
            background: #e8f5e8;
            border: 1px solid #c8e6c9;
            color: #2e7d32;
        }
        .info-box.amber {
            background: #fff8e1;
            border: 1px solid #ffecb3;
            color: #f57f17;
        }
        .info-box h4 {
            margin: 0 0 8px 0;
            font-size: 14px;
            font-weight: 600;
            display: flex;
            align-items: center;
            gap: 6px;
        }
        .info-box ul {
            margin: 0;
            padding-left: 20px;
        }
        .info-box li {
            margin: 4px 0;
            font-size: 12px;
        }
        .btn {
            width: 100%;
            padding: 12px 16px;
            border: none;
            border-radius: 6px;
            font-size: 14px;
            font-weight: 500;
            cursor: pointer;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 8px;
            margin-bottom: 12px;
            transition: all 0.2s;
        }
        .btn-primary {
            background: #007bff;
            color: white;
        }
        .btn-primary:hover {
            background: #0056b3;
        }
        .btn-outline {
            background: white;
            color: #6c757d;
            border: 1px solid #dee2e6;
        }
        .btn-outline:hover {
            background: #f8f9fa;
            border-color: #adb5bd;
        }
        .note {
            text-align: center;
            font-size: 12px;
            color: #6c757d;
        }
        .explanation {
            background: white;
            border: 1px solid #e9ecef;
            border-radius: 8px;
            padding: 16px;
        }
        .explanation-grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 20px;
        }
        @media (max-width: 768px) {
            .explanation-grid {
                grid-template-columns: 1fr;
            }
        }
        .explanation h4 {
            margin: 0 0 8px 0;
            font-size: 14px;
            font-weight: 600;
        }
        .explanation ul {
            margin: 0;
            padding-left: 16px;
        }
        .explanation li {
            margin: 2px 0;
            font-size: 12px;
            color: #6c757d;
        }
        .icon {
            width: 16px;
            height: 16px;
            display: inline-block;
        }
        .success {
            background: #d4edda;
            border: 1px solid #c3e6cb;
            color: #155724;
            padding: 16px;
            border-radius: 8px;
            margin-bottom: 20px;
        }
        .improvement {
            background: #fff3cd;
            border: 1px solid #ffeaa7;
            color: #856404;
            padding: 16px;
            border-radius: 8px;
            margin-bottom: 20px;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>📊 数据导出页面布局优化</h1>
            <p>重新设计为左右分栏布局，解决100%比例下按钮不可见的问题</p>
        </div>

        <div class="success">
            <h3>✅ 问题已解决</h3>
            <p><strong>原问题</strong>：在100%页面比例下，用户需要滚动才能看到下载按钮</p>
            <p><strong>解决方案</strong>：采用响应式左右分栏布局，优化空间利用率</p>
        </div>

        <div class="improvement">
            <h3>🎯 布局改进</h3>
            <ul>
                <li><strong>桌面端</strong>：左右两栏并排显示，充分利用横向空间</li>
                <li><strong>移动端</strong>：自动切换为单栏布局，保持良好的移动体验</li>
                <li><strong>内容精简</strong>：压缩说明文字，突出核心功能</li>
                <li><strong>视觉优化</strong>：统一卡片高度，整体更协调</li>
            </ul>
        </div>

        <!-- 模拟新的布局 -->
        <div class="grid">
            <!-- JSON 数据导出 -->
            <div class="card">
                <div class="card-header">
                    <h3 class="card-title">
                        <span class="icon">📥</span>
                        JSON 数据导出
                    </h3>
                    <p class="card-description">导出所有工具和分类数据为 JSON 文件</p>
                </div>
                <div class="card-content">
                    <div class="info-box blue">
                        <h4>
                            <span class="icon">✅</span>
                            导出内容：
                        </h4>
                        <ul>
                            <li>• 所有工具数据（31 个工具）</li>
                            <li>• 完整分类结构（9 个分类）</li>
                            <li>• 导出时间戳和版本信息</li>
                            <li>• 数据完整性校验信息</li>
                        </ul>
                    </div>

                    <button class="btn btn-primary">
                        <span class="icon">📥</span>
                        下载 JSON 数据文件
                    </button>

                    <div class="note">
                        可在其他 ToolMaster 实例中导入使用
                    </div>
                </div>
            </div>

            <!-- 浏览器书签导出 -->
            <div class="card">
                <div class="card-header">
                    <h3 class="card-title">
                        <span class="icon">📄</span>
                        浏览器书签导出
                    </h3>
                    <p class="card-description">导出为浏览器书签格式（HTML）</p>
                </div>
                <div class="card-content">
                    <div class="info-box green">
                        <h4>
                            <span class="icon">✅</span>
                            书签特点：
                        </h4>
                        <ul>
                            <li>• 按三级分类结构组织</li>
                            <li>• 保持原有的分类层次关系</li>
                            <li>• 兼容所有主流浏览器</li>
                            <li>• 便于在其他设备上快速访问</li>
                        </ul>
                    </div>

                    <div class="info-box amber">
                        <h4>
                            <span class="icon">⚠️</span>
                            使用说明：
                        </h4>
                        <p style="margin: 0; font-size: 12px;">
                            下载后可通过浏览器的"导入书签"功能导入
                        </p>
                    </div>

                    <button class="btn btn-outline">
                        <span class="icon">📄</span>
                        下载浏览器书签文件
                    </button>

                    <div class="note">
                        符合 Netscape 书签格式标准
                    </div>
                </div>
            </div>
        </div>

        <!-- 导出说明 -->
        <div class="explanation">
            <h3 style="margin: 0 0 16px 0; display: flex; align-items: center; gap: 8px;">
                <span class="icon">⚠️</span>
                导出说明
            </h3>
            <div class="explanation-grid">
                <div>
                    <h4 style="color: #007bff;">JSON 格式用途：</h4>
                    <ul>
                        <li>• 数据备份和恢复</li>
                        <li>• 跨平台数据迁移</li>
                        <li>• 开发和测试环境同步</li>
                        <li>• 数据分析和处理</li>
                    </ul>
                </div>
                <div>
                    <h4 style="color: #28a745;">书签格式用途：</h4>
                    <ul>
                        <li>• 浏览器直接访问工具</li>
                        <li>• 跨设备快速同步</li>
                        <li>• 离线工具链接访问</li>
                        <li>• 团队工具集分享</li>
                    </ul>
                </div>
            </div>
        </div>

        <div class="success" style="margin-top: 24px;">
            <h3>🎉 优化效果</h3>
            <p>现在在100%页面比例下，用户可以直接看到所有导出选项和下载按钮，无需滚动页面。</p>
            <p><strong>访问测试</strong>：请访问 <a href="http://localhost:3001" target="_blank">http://localhost:3001</a> → 数据管理 → 数据导出 查看实际效果</p>
        </div>
    </div>

    <script>
        console.log('📊 数据导出页面布局优化完成');
        console.log('🔗 请访问 http://localhost:3001 测试新布局');
        
        // 模拟按钮点击效果
        document.querySelectorAll('.btn').forEach(btn => {
            btn.addEventListener('click', function() {
                this.style.transform = 'scale(0.98)';
                setTimeout(() => {
                    this.style.transform = 'scale(1)';
                }, 100);
            });
        });
    </script>
</body>
</html>
