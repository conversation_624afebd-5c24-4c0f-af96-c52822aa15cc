<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>批量导入URL测试 - ToolMaster</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            border-radius: 8px;
            padding: 30px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        h1 {
            color: #333;
            text-align: center;
            margin-bottom: 30px;
        }
        .test-section {
            margin-bottom: 30px;
            padding: 20px;
            border: 1px solid #e0e0e0;
            border-radius: 6px;
            background-color: #fafafa;
        }
        .test-title {
            font-size: 18px;
            font-weight: bold;
            color: #2563eb;
            margin-bottom: 15px;
        }
        textarea {
            width: 100%;
            height: 150px;
            padding: 12px;
            border: 1px solid #ddd;
            border-radius: 4px;
            font-family: monospace;
            font-size: 14px;
            resize: vertical;
        }
        button {
            background-color: #2563eb;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            font-size: 14px;
            margin-top: 10px;
        }
        button:hover {
            background-color: #1d4ed8;
        }
        button:disabled {
            background-color: #9ca3af;
            cursor: not-allowed;
        }
        .result {
            margin-top: 15px;
            padding: 15px;
            border-radius: 4px;
            font-family: monospace;
            font-size: 12px;
            white-space: pre-wrap;
        }
        .result.success {
            background-color: #dcfce7;
            border: 1px solid #16a34a;
            color: #15803d;
        }
        .result.error {
            background-color: #fef2f2;
            border: 1px solid #dc2626;
            color: #dc2626;
        }
        .result.info {
            background-color: #dbeafe;
            border: 1px solid #2563eb;
            color: #1d4ed8;
        }
        .loading {
            display: inline-block;
            width: 16px;
            height: 16px;
            border: 2px solid #f3f3f3;
            border-top: 2px solid #2563eb;
            border-radius: 50%;
            animation: spin 1s linear infinite;
            margin-right: 8px;
        }
        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
        .instructions {
            background-color: #f0f9ff;
            border: 1px solid #0ea5e9;
            border-radius: 6px;
            padding: 20px;
            margin-bottom: 30px;
        }
        .instructions h3 {
            color: #0369a1;
            margin-top: 0;
        }
        .instructions ul {
            margin: 10px 0;
            padding-left: 20px;
        }
        .instructions li {
            margin-bottom: 5px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔧 批量导入URL测试</h1>
        
        <div class="instructions">
            <h3>📋 测试说明</h3>
            <p>此页面用于测试批量导入功能的URL分析能力。修复后的功能应该能够：</p>
            <ul>
                <li>✅ 自动检测输入内容是否为URL列表</li>
                <li>✅ 对每个URL单独进行AI分析</li>
                <li>✅ 获取真实的工具名称、描述、分类和标签</li>
                <li>✅ 提供详细的进度反馈</li>
                <li>✅ 处理分析失败的URL（使用基本信息）</li>
            </ul>
        </div>

        <!-- 测试1：URL列表检测 -->
        <div class="test-section">
            <div class="test-title">🔍 测试1：URL列表检测功能</div>
            <p>测试系统是否能正确识别URL列表内容</p>
            <textarea id="urlDetectionInput" placeholder="请输入测试内容...">https://github.com
https://www.figma.com
https://chat.openai.com
https://www.notion.so
https://vercel.com</textarea>
            <br>
            <button onclick="testUrlDetection()">测试URL检测</button>
            <div id="urlDetectionResult" class="result" style="display: none;"></div>
        </div>

        <!-- 测试2：单个URL分析 -->
        <div class="test-section">
            <div class="test-title">🎯 测试2：单个URL分析</div>
            <p>测试单个URL的AI分析功能</p>
            <textarea id="singleUrlInput" placeholder="请输入一个URL...">https://github.com</textarea>
            <br>
            <button onclick="testSingleUrl()" id="singleUrlBtn">分析单个URL</button>
            <div id="singleUrlResult" class="result" style="display: none;"></div>
        </div>

        <!-- 测试3：批量URL导入模拟 -->
        <div class="test-section">
            <div class="test-title">📦 测试3：批量URL导入模拟</div>
            <p>模拟完整的批量导入流程</p>
            <textarea id="batchUrlInput" placeholder="请输入多个URL...">https://github.com
https://www.figma.com
https://chat.openai.com</textarea>
            <br>
            <button onclick="testBatchImport()" id="batchImportBtn">开始批量导入测试</button>
            <div id="batchImportResult" class="result" style="display: none;"></div>
        </div>

        <!-- 测试4：混合内容测试 -->
        <div class="test-section">
            <div class="test-title">🔀 测试4：混合内容测试</div>
            <p>测试包含URL和其他内容的混合输入</p>
            <textarea id="mixedContentInput" placeholder="请输入混合内容...">一些工具推荐：
https://github.com - 代码托管平台
https://www.figma.com - 设计工具
还有一些文本描述...</textarea>
            <br>
            <button onclick="testMixedContent()">测试混合内容</button>
            <div id="mixedContentResult" class="result" style="display: none;"></div>
        </div>
    </div>

    <script>
        // 模拟URL检测函数
        function isUrlListContent(content) {
            const lines = content.split('\n').filter(line => line.trim())
            if (lines.length === 0) return false

            const urlRegex = /^https?:\/\/[^\s<>"']+/
            const urlLines = lines.filter(line => urlRegex.test(line.trim()))
            const urlRatio = urlLines.length / lines.length

            return urlRatio >= 0.7 && urlLines.length > 0
        }

        // 测试URL检测功能
        function testUrlDetection() {
            const input = document.getElementById('urlDetectionInput').value
            const result = document.getElementById('urlDetectionResult')
            
            const isUrlList = isUrlListContent(input)
            const lines = input.split('\n').filter(line => line.trim())
            const urlRegex = /^https?:\/\/[^\s<>"']+/
            const urlLines = lines.filter(line => urlRegex.test(line.trim()))
            
            result.className = `result ${isUrlList ? 'success' : 'info'}`
            result.style.display = 'block'
            result.textContent = `检测结果：${isUrlList ? '✅ 识别为URL列表' : '❌ 不是URL列表'}

详细信息：
- 总行数：${lines.length}
- URL行数：${urlLines.length}
- URL比例：${((urlLines.length / lines.length) * 100).toFixed(1)}%
- 阈值：70%

识别的URL：
${urlLines.map((url, i) => `${i + 1}. ${url}`).join('\n')}`
        }

        // 测试单个URL分析
        async function testSingleUrl() {
            const input = document.getElementById('singleUrlInput').value.trim()
            const result = document.getElementById('singleUrlResult')
            const btn = document.getElementById('singleUrlBtn')
            
            if (!input) {
                result.className = 'result error'
                result.style.display = 'block'
                result.textContent = '请输入一个有效的URL'
                return
            }

            btn.disabled = true
            btn.innerHTML = '<span class="loading"></span>分析中...'
            result.style.display = 'block'
            result.className = 'result info'
            result.textContent = '正在调用API分析URL...'

            try {
                const response = await fetch('/api/analyze-url', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'X-API-Key': localStorage.getItem('toolmaster-api-key') || 'test-key'
                    },
                    body: JSON.stringify({ url: input })
                })

                if (!response.ok) {
                    throw new Error(`HTTP ${response.status}: ${response.statusText}`)
                }

                const data = await response.json()
                result.className = 'result success'
                result.textContent = `✅ 分析成功！

工具信息：
- 名称：${data.name}
- 描述：${data.description}
- 标签：${data.tags ? data.tags.join(', ') : '无'}
- 分类：${data.category} > ${data.subcategory} > ${data.subsubcategory}

原始响应：
${JSON.stringify(data, null, 2)}`

            } catch (error) {
                result.className = 'result error'
                result.textContent = `❌ 分析失败：${error.message}

可能的原因：
1. API密钥未设置或无效
2. 网络连接问题
3. URL无法访问
4. 服务器错误

请检查控制台获取更多信息。`
                console.error('URL分析失败:', error)
            } finally {
                btn.disabled = false
                btn.innerHTML = '分析单个URL'
            }
        }

        // 测试批量导入
        async function testBatchImport() {
            const input = document.getElementById('batchUrlInput').value.trim()
            const result = document.getElementById('batchImportResult')
            const btn = document.getElementById('batchImportBtn')
            
            if (!input) {
                result.className = 'result error'
                result.style.display = 'block'
                result.textContent = '请输入URL列表'
                return
            }

            const urls = input.split('\n')
                .filter(line => line.trim())
                .filter(line => /^https?:\/\//.test(line.trim()))
                .map(line => line.trim())

            if (urls.length === 0) {
                result.className = 'result error'
                result.style.display = 'block'
                result.textContent = '未找到有效的URL'
                return
            }

            btn.disabled = true
            btn.innerHTML = '<span class="loading"></span>处理中...'
            result.style.display = 'block'
            result.className = 'result info'
            result.textContent = `开始处理 ${urls.length} 个URL...\n`

            const results = []
            for (let i = 0; i < urls.length; i++) {
                const url = urls[i]
                result.textContent += `\n正在处理 ${i + 1}/${urls.length}: ${url}`
                
                try {
                    const response = await fetch('/api/analyze-url', {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json',
                            'X-API-Key': localStorage.getItem('toolmaster-api-key') || 'test-key'
                        },
                        body: JSON.stringify({ url })
                    })

                    if (response.ok) {
                        const data = await response.json()
                        results.push({ url, success: true, data })
                        result.textContent += ` ✅ ${data.name}`
                    } else {
                        results.push({ url, success: false, error: `HTTP ${response.status}` })
                        result.textContent += ` ❌ 失败`
                    }
                } catch (error) {
                    results.push({ url, success: false, error: error.message })
                    result.textContent += ` ❌ 错误`
                }

                // 添加延迟避免API限制
                if (i < urls.length - 1) {
                    await new Promise(resolve => setTimeout(resolve, 500))
                }
            }

            const successCount = results.filter(r => r.success).length
            const failCount = results.filter(r => !r.success).length

            result.className = successCount > 0 ? 'result success' : 'result error'
            result.textContent += `\n\n📊 处理完成：
- 成功：${successCount}
- 失败：${failCount}
- 总计：${results.length}

详细结果：
${results.map((r, i) => 
    `${i + 1}. ${r.url}\n   ${r.success ? `✅ ${r.data.name} - ${r.data.description.substring(0, 50)}...` : `❌ ${r.error}`}`
).join('\n')}`

            btn.disabled = false
            btn.innerHTML = '开始批量导入测试'
        }

        // 测试混合内容
        function testMixedContent() {
            const input = document.getElementById('mixedContentInput').value
            const result = document.getElementById('mixedContentResult')
            
            const isUrlList = isUrlListContent(input)
            const lines = input.split('\n').filter(line => line.trim())
            const urlRegex = /^https?:\/\/[^\s<>"']+/
            const urlLines = lines.filter(line => urlRegex.test(line.trim()))
            const nonUrlLines = lines.filter(line => !urlRegex.test(line.trim()))
            
            result.className = 'result info'
            result.style.display = 'block'
            result.textContent = `混合内容分析：

检测结果：${isUrlList ? '✅ 将作为URL列表处理' : '❌ 将作为普通文本处理'}

内容统计：
- 总行数：${lines.length}
- URL行数：${urlLines.length}
- 文本行数：${nonUrlLines.length}
- URL比例：${((urlLines.length / lines.length) * 100).toFixed(1)}%

处理方式：
${isUrlList ? 
    '🎯 使用单独URL分析模式\n- 每个URL将单独调用AI分析\n- 获取真实的工具信息' : 
    '📝 使用AI批量分析模式\n- 整体内容交给AI处理\n- 可能提取URL和描述信息'
}

识别的URL：
${urlLines.map((url, i) => `${i + 1}. ${url}`).join('\n') || '无'}

其他文本：
${nonUrlLines.map((line, i) => `${i + 1}. ${line}`).join('\n') || '无'}`
        }

        // 页面加载时的初始化
        document.addEventListener('DOMContentLoaded', function() {
            console.log('批量导入URL测试页面已加载')
            
            // 检查API密钥
            const apiKey = localStorage.getItem('toolmaster-api-key')
            if (!apiKey) {
                console.warn('未找到API密钥，某些测试可能无法正常工作')
            }
        })
    </script>
</body>
</html>
