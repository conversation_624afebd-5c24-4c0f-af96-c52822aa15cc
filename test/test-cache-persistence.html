<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>ToolMaster 缓存持久性测试</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            max-width: 1000px;
            margin: 0 auto;
            padding: 20px;
            line-height: 1.6;
            background: #f8fafc;
        }
        .header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 30px;
            border-radius: 12px;
            margin-bottom: 30px;
            text-align: center;
        }
        .test-section {
            background: white;
            padding: 25px;
            margin-bottom: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .test-button {
            background: #3b82f6;
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 6px;
            cursor: pointer;
            font-size: 16px;
            margin: 10px;
        }
        .test-button:hover {
            background: #2563eb;
        }
        .danger-button {
            background: #dc2626;
        }
        .danger-button:hover {
            background: #b91c1c;
        }
        .success-button {
            background: #059669;
        }
        .success-button:hover {
            background: #047857;
        }
        .test-results {
            background: #f8fafc;
            border: 1px solid #e2e8f0;
            border-radius: 6px;
            padding: 15px;
            margin-top: 15px;
            font-family: monospace;
            white-space: pre-wrap;
            max-height: 400px;
            overflow-y: auto;
        }
        .status {
            padding: 10px;
            border-radius: 6px;
            margin: 10px 0;
        }
        .status.success {
            background: #dcfce7;
            color: #166534;
            border: 1px solid #bbf7d0;
        }
        .status.error {
            background: #fef2f2;
            color: #991b1b;
            border: 1px solid #fecaca;
        }
        .status.warning {
            background: #fefce8;
            color: #a16207;
            border: 1px solid #fde68a;
        }
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
            margin: 20px 0;
        }
        .stat-card {
            background: #f1f5f9;
            padding: 15px;
            border-radius: 8px;
            text-align: center;
        }
        .stat-number {
            font-size: 1.5em;
            font-weight: bold;
            color: #3b82f6;
        }
        .stat-label {
            color: #64748b;
            margin-top: 5px;
        }
    </style>
</head>
<body>
    <div class="header">
        <h1>🧪 ToolMaster 缓存持久性测试</h1>
        <p>测试分类数据在清理缓存后是否能从数据库正确恢复</p>
    </div>

    <div class="test-section">
        <h2>📊 当前状态检查</h2>
        <button class="test-button" onclick="checkCurrentState()">检查当前分类状态</button>
        <button class="test-button" onclick="checkLocalStorage()">检查本地存储</button>
        <div id="currentStateResults" class="test-results" style="display: none;"></div>
    </div>

    <div class="test-section">
        <h2>🗑️ 缓存清理测试</h2>
        <p><strong>警告</strong>: 以下操作将清理浏览器缓存，请确保您已保存重要数据！</p>
        <button class="test-button danger-button" onclick="clearLocalStorage()">清理 localStorage</button>
        <button class="test-button danger-button" onclick="clearAllCache()">清理所有缓存</button>
        <div id="clearResults" class="test-results" style="display: none;"></div>
    </div>

    <div class="test-section">
        <h2>🔄 数据恢复测试</h2>
        <button class="test-button success-button" onclick="testDataRecovery()">测试数据恢复</button>
        <button class="test-button" onclick="forceReloadFromDB()">强制从数据库重新加载</button>
        <div id="recoveryResults" class="test-results" style="display: none;"></div>
    </div>

    <div class="test-section">
        <h2>📈 测试统计</h2>
        <div class="stats-grid" id="statsGrid">
            <!-- 统计信息将在这里显示 -->
        </div>
    </div>

    <script>
        let testResults = {
            beforeClear: null,
            afterClear: null,
            afterRecovery: null
        };

        // 检查当前分类状态
        function checkCurrentState() {
            const results = [];
            results.push('🔍 检查当前分类状态...\n');

            try {
                // 检查 localStorage
                const categories = JSON.parse(localStorage.getItem('toolmaster-categories') || '[]');
                const tools = JSON.parse(localStorage.getItem('toolmaster-tools') || '[]');
                
                results.push(`📦 localStorage 分类数量: ${categories.length}`);
                results.push(`🔧 localStorage 工具数量: ${tools.length}`);

                // 统计分类结构
                let totalSubcategories = 0;
                let totalSubsubcategories = 0;
                
                categories.forEach(cat => {
                    totalSubcategories += cat.subcategories?.length || 0;
                    cat.subcategories?.forEach(sub => {
                        totalSubsubcategories += sub.subsubcategories?.length || 0;
                    });
                });

                results.push(`📊 二级分类总数: ${totalSubcategories}`);
                results.push(`📊 三级分类总数: ${totalSubsubcategories}`);

                // 检查新增分类
                const newCategories = [
                    'time-management', 'communication', 'skill-training', 'academic-research',
                    'creative-entertainment', 'interactive-entertainment', 'shopping', 'home-living',
                    'human-resources', 'financial-management', 'system-optimization', 'hardware-tools',
                    'voice-processing', 'intelligent-decision', 'development-helper', 'fun-tools'
                ];

                let foundNewCategories = 0;
                categories.forEach(cat => {
                    cat.subcategories?.forEach(sub => {
                        if (newCategories.includes(sub.id)) {
                            foundNewCategories++;
                        }
                    });
                });

                results.push(`✨ 新增分类检测: ${foundNewCategories}/16`);

                testResults.beforeClear = {
                    categories: categories.length,
                    subcategories: totalSubcategories,
                    subsubcategories: totalSubsubcategories,
                    newCategories: foundNewCategories,
                    tools: tools.length
                };

                if (foundNewCategories === 16 && totalSubcategories === 60 && totalSubsubcategories === 240) {
                    results.push('\n✅ 分类结构完整，所有新增分类都存在！');
                } else {
                    results.push('\n⚠️ 分类结构可能不完整');
                }

            } catch (error) {
                results.push(`❌ 检查失败: ${error.message}`);
            }

            document.getElementById('currentStateResults').style.display = 'block';
            document.getElementById('currentStateResults').textContent = results.join('\n');
            updateStats();
        }

        // 检查本地存储
        function checkLocalStorage() {
            const results = [];
            results.push('🔍 检查本地存储详情...\n');

            const storageKeys = [
                'toolmaster-categories',
                'toolmaster-tools', 
                'toolmaster-logs',
                'toolmaster-password',
                'toolmaster-theme'
            ];

            storageKeys.forEach(key => {
                const value = localStorage.getItem(key);
                if (value) {
                    const size = new Blob([value]).size;
                    results.push(`📦 ${key}: ${size} bytes`);
                } else {
                    results.push(`❌ ${key}: 不存在`);
                }
            });

            document.getElementById('currentStateResults').style.display = 'block';
            document.getElementById('currentStateResults').textContent = results.join('\n');
        }

        // 清理 localStorage
        function clearLocalStorage() {
            if (!confirm('确定要清理 localStorage 吗？这将删除所有本地缓存数据！')) {
                return;
            }

            const results = [];
            results.push('🗑️ 清理 localStorage...\n');

            try {
                const beforeKeys = Object.keys(localStorage).filter(key => key.startsWith('toolmaster-'));
                results.push(`清理前的键: ${beforeKeys.join(', ')}`);

                // 清理所有 toolmaster 相关的键
                beforeKeys.forEach(key => {
                    localStorage.removeItem(key);
                    results.push(`🗑️ 已删除: ${key}`);
                });

                const afterKeys = Object.keys(localStorage).filter(key => key.startsWith('toolmaster-'));
                results.push(`\n清理后的键: ${afterKeys.length === 0 ? '无' : afterKeys.join(', ')}`);

                results.push('\n✅ localStorage 清理完成！');

            } catch (error) {
                results.push(`❌ 清理失败: ${error.message}`);
            }

            document.getElementById('clearResults').style.display = 'block';
            document.getElementById('clearResults').textContent = results.join('\n');
        }

        // 清理所有缓存
        function clearAllCache() {
            if (!confirm('确定要清理所有缓存吗？这将删除所有缓存数据并刷新页面！')) {
                return;
            }

            const results = [];
            results.push('🗑️ 清理所有缓存...\n');

            try {
                // 清理 localStorage
                localStorage.clear();
                results.push('✅ localStorage 已清理');

                // 清理 sessionStorage
                sessionStorage.clear();
                results.push('✅ sessionStorage 已清理');

                // 尝试清理 Service Worker 缓存
                if ('serviceWorker' in navigator) {
                    navigator.serviceWorker.getRegistrations().then(registrations => {
                        registrations.forEach(registration => {
                            registration.unregister();
                        });
                    });
                    results.push('✅ Service Worker 已清理');
                }

                results.push('\n🔄 3秒后将刷新页面...');

                document.getElementById('clearResults').style.display = 'block';
                document.getElementById('clearResults').textContent = results.join('\n');

                // 3秒后刷新页面
                setTimeout(() => {
                    window.location.reload(true);
                }, 3000);

            } catch (error) {
                results.push(`❌ 清理失败: ${error.message}`);
                document.getElementById('clearResults').style.display = 'block';
                document.getElementById('clearResults').textContent = results.join('\n');
            }
        }

        // 测试数据恢复
        function testDataRecovery() {
            const results = [];
            results.push('🔄 测试数据恢复...\n');

            try {
                // 检查清理后的状态
                const categories = JSON.parse(localStorage.getItem('toolmaster-categories') || '[]');
                const tools = JSON.parse(localStorage.getItem('toolmaster-tools') || '[]');

                results.push(`📦 当前 localStorage 分类数量: ${categories.length}`);
                results.push(`🔧 当前 localStorage 工具数量: ${tools.length}`);

                if (categories.length === 0) {
                    results.push('\n⚠️ 本地缓存为空，这是正常的清理后状态');
                    results.push('💡 请打开 ToolMaster 主页，应用会自动从数据库恢复数据');
                    results.push('🔗 <a href="http://localhost:3000" target="_blank">点击打开 ToolMaster</a>');
                } else {
                    // 统计恢复后的数据
                    let totalSubcategories = 0;
                    let totalSubsubcategories = 0;
                    
                    categories.forEach(cat => {
                        totalSubcategories += cat.subcategories?.length || 0;
                        cat.subcategories?.forEach(sub => {
                            totalSubsubcategories += sub.subsubcategories?.length || 0;
                        });
                    });

                    results.push(`📊 恢复的二级分类总数: ${totalSubcategories}`);
                    results.push(`📊 恢复的三级分类总数: ${totalSubsubcategories}`);

                    testResults.afterRecovery = {
                        categories: categories.length,
                        subcategories: totalSubcategories,
                        subsubcategories: totalSubsubcategories,
                        tools: tools.length
                    };

                    if (totalSubcategories === 60 && totalSubsubcategories === 240) {
                        results.push('\n✅ 数据恢复成功！所有分类都已从数据库恢复');
                    } else {
                        results.push('\n⚠️ 数据恢复可能不完整，请检查数据库连接');
                    }
                }

            } catch (error) {
                results.push(`❌ 恢复测试失败: ${error.message}`);
            }

            document.getElementById('recoveryResults').style.display = 'block';
            document.getElementById('recoveryResults').textContent = results.join('\n');
            updateStats();
        }

        // 强制从数据库重新加载
        function forceReloadFromDB() {
            const results = [];
            results.push('🔄 强制从数据库重新加载...\n');

            try {
                // 清理本地缓存
                localStorage.removeItem('toolmaster-categories');
                localStorage.removeItem('toolmaster-tools');
                
                results.push('✅ 已清理本地缓存');
                results.push('🔄 正在重新加载页面...');

                document.getElementById('recoveryResults').style.display = 'block';
                document.getElementById('recoveryResults').textContent = results.join('\n');

                // 重新加载页面
                setTimeout(() => {
                    window.location.href = 'http://localhost:3000';
                }, 2000);

            } catch (error) {
                results.push(`❌ 重新加载失败: ${error.message}`);
                document.getElementById('recoveryResults').style.display = 'block';
                document.getElementById('recoveryResults').textContent = results.join('\n');
            }
        }

        // 更新统计信息
        function updateStats() {
            const current = testResults.beforeClear || testResults.afterRecovery;
            if (!current) return;

            const statsHtml = `
                <div class="stat-card">
                    <div class="stat-number">${current.categories}</div>
                    <div class="stat-label">一级分类</div>
                </div>
                <div class="stat-card">
                    <div class="stat-number">${current.subcategories}</div>
                    <div class="stat-label">二级分类</div>
                </div>
                <div class="stat-card">
                    <div class="stat-number">${current.subsubcategories}</div>
                    <div class="stat-label">三级分类</div>
                </div>
                <div class="stat-card">
                    <div class="stat-number">${current.newCategories || 'N/A'}</div>
                    <div class="stat-label">新增分类</div>
                </div>
                <div class="stat-card">
                    <div class="stat-number">${current.tools}</div>
                    <div class="stat-label">工具数量</div>
                </div>
            `;
            document.getElementById('statsGrid').innerHTML = statsHtml;
        }

        // 页面加载时自动检查状态
        document.addEventListener('DOMContentLoaded', function() {
            checkCurrentState();
        });
    </script>
</body>
</html>
