/**
 * 书签导出功能验证脚本
 * 用于验证生成的书签HTML格式是否正确
 */

// 模拟测试数据
const testTools = [
  {
    id: "1",
    name: "GitHub",
    url: "https://github.com",
    description: "代码托管平台",
    tags: ["代码", "版本控制"],
    category: "development",
    subcategory: "version-control",
    subsubcategory: "git-tools",
    addedAt: "2024-01-15T10:30:00.000Z",
    sensitive: false
  },
  {
    id: "2",
    name: "Figma",
    url: "https://figma.com",
    description: "UI设计工具",
    tags: ["设计", "UI"],
    category: "design",
    subcategory: "ui-design",
    subsubcategory: "prototyping",
    addedAt: "2024-01-16T14:20:00.000Z",
    sensitive: false
  },
  {
    id: "3",
    name: "ChatGPT",
    url: "https://chat.openai.com",
    description: "AI对话助手",
    tags: ["AI", "对话"],
    category: "ai-tools",
    subcategory: "text-generation",
    subsubcategory: "chatbot",
    addedAt: "2024-01-17T09:15:00.000Z",
    sensitive: false
  }
];

const testCategories = [
  {
    id: "development",
    name: "开发工具",
    subcategories: [
      {
        id: "version-control",
        name: "版本控制",
        subsubcategories: [
          { id: "git-tools", name: "Git工具" }
        ]
      }
    ]
  },
  {
    id: "design",
    name: "设计工具",
    subcategories: [
      {
        id: "ui-design",
        name: "UI设计",
        subsubcategories: [
          { id: "prototyping", name: "原型设计" }
        ]
      }
    ]
  },
  {
    id: "ai-tools",
    name: "AI工具",
    subcategories: [
      {
        id: "text-generation",
        name: "文本生成",
        subsubcategories: [
          { id: "chatbot", name: "聊天机器人" }
        ]
      }
    ]
  }
];

// 书签导出器类（简化版，用于测试）
class BookmarkExporter {
  static exportToBookmarks(tools, categories) {
    const timestamp = Math.floor(Date.now() / 1000);
    
    let html = `<!DOCTYPE NETSCAPE-Bookmark-file-1>
<!-- This is an automatically generated file.
     It will be read and overwritten.
     DO NOT EDIT! -->
<META HTTP-EQUIV="Content-Type" CONTENT="text/html; charset=UTF-8">
<TITLE>Bookmarks</TITLE>
<H1>Bookmarks</H1>
<DL><p>
    <DT><H3 ADD_DATE="${timestamp}" LAST_MODIFIED="${timestamp}" PERSONAL_TOOLBAR_FOLDER="true">书签栏</H3>
    <DL><p>
        <DT><H3 ADD_DATE="${timestamp}" LAST_MODIFIED="${timestamp}">ToolMaster 工具箱</H3>
        <DL><p>
`;

    const toolsByCategory = this.organizeToolsByCategory(tools, categories);
    
    categories.forEach(category => {
      const categoryTools = toolsByCategory[category.id] || {};
      
      if (Object.keys(categoryTools).length === 0) {
        return;
      }
      
      html += `            <DT><H3 ADD_DATE="${timestamp}" LAST_MODIFIED="${timestamp}">${this.escapeHtml(category.name)}</H3>\n`;
      html += `            <DL><p>\n`;
      
      category.subcategories.forEach(subcategory => {
        const subcategoryTools = categoryTools[subcategory.id] || {};
        
        if (Object.keys(subcategoryTools).length === 0) {
          return;
        }
        
        html += `                <DT><H3 ADD_DATE="${timestamp}" LAST_MODIFIED="${timestamp}">${this.escapeHtml(subcategory.name)}</H3>\n`;
        html += `                <DL><p>\n`;
        
        subcategory.subsubcategories.forEach(subsubcategory => {
          const subsubcategoryTools = subcategoryTools[subsubcategory.id] || [];
          
          if (subsubcategoryTools.length === 0) {
            return;
          }
          
          html += `                    <DT><H3 ADD_DATE="${timestamp}" LAST_MODIFIED="${timestamp}">${this.escapeHtml(subsubcategory.name)}</H3>\n`;
          html += `                    <DL><p>\n`;
          
          subsubcategoryTools.forEach(tool => {
            const toolTimestamp = Math.floor(new Date(tool.addedAt).getTime() / 1000);
            html += `                        <DT><A HREF="${this.escapeHtml(tool.url)}" ADD_DATE="${toolTimestamp}">${this.escapeHtml(tool.name)}</A>\n`;
          });
          
          html += `                    </DL><p>\n`;
        });
        
        html += `                </DL><p>\n`;
      });
      
      html += `            </DL><p>\n`;
    });

    html += `        </DL><p>
    </DL><p>
</DL><p>
`;

    return html;
  }

  static organizeToolsByCategory(tools, categories) {
    const organized = {};
    
    categories.forEach(category => {
      organized[category.id] = {};
      category.subcategories.forEach(subcategory => {
        organized[category.id][subcategory.id] = {};
        subcategory.subsubcategories.forEach(subsubcategory => {
          organized[category.id][subcategory.id][subsubcategory.id] = [];
        });
      });
    });
    
    tools.forEach(tool => {
      if (organized[tool.category] && 
          organized[tool.category][tool.subcategory] && 
          organized[tool.category][tool.subcategory][tool.subsubcategory]) {
        organized[tool.category][tool.subcategory][tool.subsubcategory].push(tool);
      }
    });
    
    return organized;
  }

  static escapeHtml(text) {
    // Node.js环境下的HTML转义
    if (typeof document === 'undefined') {
      return text
        .replace(/&/g, '&amp;')
        .replace(/</g, '&lt;')
        .replace(/>/g, '&gt;')
        .replace(/"/g, '&quot;')
        .replace(/'/g, '&#39;');
    }
    // 浏览器环境下的HTML转义
    const div = document.createElement('div');
    div.textContent = text;
    return div.innerHTML;
  }

  static getExportStats(tools, categories) {
    const toolsByCategory = this.organizeToolsByCategory(tools, categories);
    
    let totalCategories = 0;
    let totalSubcategories = 0;
    let totalSubsubcategories = 0;
    let totalTools = 0;
    
    categories.forEach(category => {
      const categoryTools = toolsByCategory[category.id] || {};
      const hasTools = Object.values(categoryTools).some(subcategoryTools => 
        Object.values(subcategoryTools).some(tools => tools.length > 0)
      );
      
      if (hasTools) {
        totalCategories++;
        
        category.subcategories.forEach(subcategory => {
          const subcategoryTools = categoryTools[subcategory.id] || {};
          const hasSubcategoryTools = Object.values(subcategoryTools).some(tools => tools.length > 0);
          
          if (hasSubcategoryTools) {
            totalSubcategories++;
            
            subcategory.subsubcategories.forEach(subsubcategory => {
              const subsubcategoryTools = subcategoryTools[subsubcategory.id] || [];
              
              if (subsubcategoryTools.length > 0) {
                totalSubsubcategories++;
                totalTools += subsubcategoryTools.length;
              }
            });
          }
        });
      }
    });
    
    return {
      totalCategories,
      totalSubcategories,
      totalSubsubcategories,
      totalTools
    };
  }
}

// 验证函数
function validateBookmarkExport() {
  console.log('🧪 开始验证书签导出功能...');
  
  try {
    // 生成书签HTML
    const bookmarkHtml = BookmarkExporter.exportToBookmarks(testTools, testCategories);
    
    // 获取统计信息
    const stats = BookmarkExporter.getExportStats(testTools, testCategories);
    
    console.log('📊 导出统计:', stats);
    
    // 验证HTML格式
    const validations = [
      {
        name: 'DOCTYPE声明',
        test: () => bookmarkHtml.includes('<!DOCTYPE NETSCAPE-Bookmark-file-1>'),
        expected: true
      },
      {
        name: 'UTF-8编码',
        test: () => bookmarkHtml.includes('charset=UTF-8'),
        expected: true
      },
      {
        name: '包含工具箱根目录',
        test: () => bookmarkHtml.includes('ToolMaster 工具箱'),
        expected: true
      },
      {
        name: '包含GitHub工具',
        test: () => bookmarkHtml.includes('GitHub') && bookmarkHtml.includes('https://github.com'),
        expected: true
      },
      {
        name: '包含Figma工具',
        test: () => bookmarkHtml.includes('Figma') && bookmarkHtml.includes('https://figma.com'),
        expected: true
      },
      {
        name: '包含ChatGPT工具',
        test: () => bookmarkHtml.includes('ChatGPT') && bookmarkHtml.includes('https://chat.openai.com'),
        expected: true
      },
      {
        name: '正确的分类层次',
        test: () => {
          const developmentIndex = bookmarkHtml.indexOf('开发工具');
          const versionControlIndex = bookmarkHtml.indexOf('版本控制');
          const gitToolsIndex = bookmarkHtml.indexOf('Git工具');
          return developmentIndex < versionControlIndex && versionControlIndex < gitToolsIndex;
        },
        expected: true
      }
    ];
    
    console.log('\n🔍 验证结果:');
    let allPassed = true;
    
    validations.forEach(validation => {
      const result = validation.test();
      const status = result === validation.expected ? '✅' : '❌';
      console.log(`${status} ${validation.name}: ${result}`);
      if (result !== validation.expected) {
        allPassed = false;
      }
    });
    
    console.log(`\n📈 统计信息:`);
    console.log(`- 总分类数: ${stats.totalCategories}`);
    console.log(`- 总子分类数: ${stats.totalSubcategories}`);
    console.log(`- 总子子分类数: ${stats.totalSubsubcategories}`);
    console.log(`- 总工具数: ${stats.totalTools}`);
    
    if (allPassed) {
      console.log('\n🎉 所有验证通过！书签导出功能正常工作。');
    } else {
      console.log('\n⚠️ 部分验证失败，请检查实现。');
    }
    
    // 输出部分HTML内容用于检查
    console.log('\n📄 生成的HTML片段（前500字符）:');
    console.log(bookmarkHtml.substring(0, 500) + '...');
    
    return allPassed;
    
  } catch (error) {
    console.error('❌ 验证过程中发生错误:', error);
    return false;
  }
}

// 如果在Node.js环境中运行
if (typeof module !== 'undefined' && module.exports) {
  module.exports = { BookmarkExporter, validateBookmarkExport, testTools, testCategories };

  // 在Node.js环境中自动运行验证
  validateBookmarkExport();
}

// 如果在浏览器环境中运行
if (typeof window !== 'undefined') {
  window.BookmarkExporter = BookmarkExporter;
  window.validateBookmarkExport = validateBookmarkExport;

  // 自动运行验证
  document.addEventListener('DOMContentLoaded', function() {
    validateBookmarkExport();
  });
}
