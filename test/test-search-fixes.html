<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>ToolMaster - 搜索修复验证测试</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            line-height: 1.6;
            background-color: #f8f9fa;
        }
        .container {
            background: white;
            border-radius: 8px;
            padding: 30px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .test-section {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 8px;
            padding: 20px;
            margin: 20px 0;
        }
        .test-section.success {
            background: #d4edda;
            border-color: #c3e6cb;
        }
        .test-section.error {
            background: #f8d7da;
            border-color: #f5c6cb;
        }
        .test-button {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
            font-size: 14px;
        }
        .test-button:hover {
            background: #0056b3;
        }
        .test-button:disabled {
            background: #6c757d;
            cursor: not-allowed;
        }
        .test-button.config {
            background: #28a745;
        }
        .test-button.config:hover {
            background: #218838;
        }
        .input-group {
            margin: 15px 0;
        }
        .input-group label {
            display: block;
            margin-bottom: 5px;
            font-weight: 500;
        }
        .input-group input, .input-group select {
            width: 100%;
            padding: 8px 12px;
            border: 1px solid #ced4da;
            border-radius: 4px;
            font-size: 14px;
        }
        .result-display {
            background: #ffffff;
            border: 1px solid #dee2e6;
            border-radius: 4px;
            padding: 15px;
            margin: 10px 0;
            max-height: 400px;
            overflow-y: auto;
        }
        .config-display {
            background: #e3f2fd;
            border: 1px solid #bbdefb;
            border-radius: 4px;
            padding: 15px;
            margin: 10px 0;
            font-family: monospace;
            font-size: 12px;
        }
        .status-indicator {
            display: inline-block;
            width: 12px;
            height: 12px;
            border-radius: 50%;
            margin-right: 8px;
        }
        .status-success { background-color: #28a745; }
        .status-error { background-color: #dc3545; }
        .status-pending { background-color: #ffc107; }
        .status-processing { background-color: #17a2b8; }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔧 ToolMaster - 搜索修复验证测试</h1>
        <p>验证搜索功能修复效果，包括AI响应解析和超时处理。</p>

        <!-- 配置管理 -->
        <div class="test-section">
            <h2>⚙️ 搜索配置管理</h2>
            
            <div class="input-group">
                <label for="apiKey">API 访问密钥:</label>
                <input type="password" id="apiKey" placeholder="输入您的API访问密钥">
            </div>

            <button class="test-button config" onclick="getCurrentConfig()">获取当前配置</button>
            <button class="test-button config" onclick="applyPreset('stable')">应用稳定模式</button>
            <button class="test-button config" onclick="applyPreset('fast')">应用快速模式</button>
            <button class="test-button config" onclick="applyPreset('hybrid')">应用混合模式</button>

            <div id="configDisplay" class="config-display" style="display: none;">
                <h4>当前配置:</h4>
                <div id="configContent"></div>
            </div>
        </div>

        <!-- 修复验证测试 -->
        <div class="test-section">
            <h2>🧪 修复效果验证</h2>
            
            <div class="input-group">
                <label for="testQuery">测试查询:</label>
                <input type="text" id="testQuery" placeholder="输入搜索关键词" value="代码编辑器">
            </div>

            <div class="input-group">
                <label for="testType">测试类型:</label>
                <select id="testType">
                    <option value="deep">深度搜索（修复AI解析）</option>
                    <option value="global">全网搜索（修复超时）</option>
                </select>
            </div>

            <button class="test-button" onclick="testOptimizedSearchFixed()">
                <span id="testButtonText">测试修复后的优化版</span>
            </button>
            <button class="test-button" onclick="testOriginalSearch()">
                <span id="originalButtonText">测试原版（对比）</span>
            </button>
            <button class="test-button" onclick="clearTestResults()">清空结果</button>

            <div id="testResults" class="result-display" style="display: none;">
                <h4>测试结果:</h4>
                <div id="testResultsContent"></div>
            </div>
        </div>

        <!-- 超时测试 -->
        <div class="test-section">
            <h2>⏱️ 超时处理测试</h2>
            <p>测试不同超时设置下的搜索表现：</p>
            
            <button class="test-button" onclick="testWithTimeout(5000)">5秒超时测试</button>
            <button class="test-button" onclick="testWithTimeout(15000)">15秒超时测试</button>
            <button class="test-button" onclick="testWithTimeout(30000)">30秒超时测试</button>

            <div id="timeoutResults" class="result-display" style="display: none;">
                <h4>超时测试结果:</h4>
                <div id="timeoutResultsContent"></div>
            </div>
        </div>

        <!-- 重试机制测试 -->
        <div class="test-section">
            <h2>🔄 重试机制测试</h2>
            <p>测试重试机制的工作效果：</p>
            
            <button class="test-button" onclick="testRetryMechanism()">测试重试机制</button>
            
            <div id="retryResults" class="result-display" style="display: none;">
                <h4>重试测试结果:</h4>
                <div id="retryResultsContent"></div>
            </div>
        </div>

        <!-- 问题修复状态 -->
        <div class="test-section">
            <h2>✅ 修复状态检查</h2>
            <table style="width: 100%; border-collapse: collapse;">
                <thead>
                    <tr style="background: #f8f9fa;">
                        <th style="border: 1px solid #dee2e6; padding: 8px;">问题</th>
                        <th style="border: 1px solid #dee2e6; padding: 8px;">修复状态</th>
                        <th style="border: 1px solid #dee2e6; padding: 8px;">验证结果</th>
                    </tr>
                </thead>
                <tbody>
                    <tr>
                        <td style="border: 1px solid #dee2e6; padding: 8px;">优化版推荐结果解析</td>
                        <td style="border: 1px solid #dee2e6; padding: 8px;">✅ 已修复</td>
                        <td style="border: 1px solid #dee2e6; padding: 8px;" id="parseFixStatus">待验证</td>
                    </tr>
                    <tr>
                        <td style="border: 1px solid #dee2e6; padding: 8px;">全网搜索超时处理</td>
                        <td style="border: 1px solid #dee2e6; padding: 8px;">✅ 已修复</td>
                        <td style="border: 1px solid #dee2e6; padding: 8px;" id="timeoutFixStatus">待验证</td>
                    </tr>
                    <tr>
                        <td style="border: 1px solid #dee2e6; padding: 8px;">重试机制</td>
                        <td style="border: 1px solid #dee2e6; padding: 8px;">✅ 已添加</td>
                        <td style="border: 1px solid #dee2e6; padding: 8px;" id="retryFixStatus">待验证</td>
                    </tr>
                    <tr>
                        <td style="border: 1px solid #dee2e6; padding: 8px;">配置管理</td>
                        <td style="border: 1px solid #dee2e6; padding: 8px;">✅ 已添加</td>
                        <td style="border: 1px solid #dee2e6; padding: 8px;" id="configFixStatus">待验证</td>
                    </tr>
                </tbody>
            </table>
        </div>
    </div>

    <script>
        // 模拟工具数据
        const mockTools = [
            { id: '1', name: 'VS Code', url: 'https://code.visualstudio.com', description: '微软开发的代码编辑器', tags: ['编辑器', '代码', 'IDE'], category: 'development', subcategory: 'code-editor', subsubcategory: 'ide' },
            { id: '2', name: 'Figma', url: 'https://figma.com', description: '在线设计工具', tags: ['设计', 'UI', '协作'], category: 'design', subcategory: 'ui-design', subsubcategory: 'design-software' },
            { id: '3', name: 'Notion', url: 'https://notion.so', description: '笔记和协作工具', tags: ['笔记', '协作', '文档'], category: 'productivity', subcategory: 'note-taking', subsubcategory: 'note-app' }
        ];

        // 获取当前配置
        async function getCurrentConfig() {
            const apiKey = document.getElementById('apiKey').value;
            if (!apiKey) {
                alert('请输入API密钥');
                return;
            }

            try {
                const response = await fetch('/api/search-config', {
                    method: 'GET',
                    headers: {
                        'X-API-Key': apiKey
                    }
                });

                const result = await response.json();
                
                const configDiv = document.getElementById('configDisplay');
                const contentDiv = document.getElementById('configContent');
                
                configDiv.style.display = 'block';
                
                if (result.success) {
                    contentDiv.innerHTML = `<pre>${JSON.stringify(result.data.currentConfig, null, 2)}</pre>`;
                    document.getElementById('configFixStatus').innerHTML = '<span class="status-indicator status-success"></span>✅ 正常';
                } else {
                    contentDiv.innerHTML = `<div style="color: #dc3545;">错误: ${result.error}</div>`;
                }
            } catch (error) {
                console.error('获取配置失败:', error);
                alert('获取配置失败: ' + error.message);
            }
        }

        // 应用配置预设
        async function applyPreset(preset) {
            const apiKey = document.getElementById('apiKey').value;
            if (!apiKey) {
                alert('请输入API密钥');
                return;
            }

            try {
                const response = await fetch('/api/search-config', {
                    method: 'PUT',
                    headers: {
                        'Content-Type': 'application/json',
                        'X-API-Key': apiKey
                    },
                    body: JSON.stringify({ preset })
                });

                const result = await response.json();
                
                if (result.success) {
                    alert(`已应用配置预设: ${preset}`);
                    getCurrentConfig(); // 刷新配置显示
                } else {
                    alert('应用预设失败: ' + result.error);
                }
            } catch (error) {
                console.error('应用预设失败:', error);
                alert('应用预设失败: ' + error.message);
            }
        }

        // 测试修复后的优化版搜索
        async function testOptimizedSearchFixed() {
            const apiKey = document.getElementById('apiKey').value;
            const query = document.getElementById('testQuery').value;
            const testType = document.getElementById('testType').value;

            if (!apiKey || !query) {
                alert('请输入API密钥和搜索查询');
                return;
            }

            const button = document.querySelector('button[onclick="testOptimizedSearchFixed()"]');
            const buttonText = document.getElementById('testButtonText');
            
            button.disabled = true;
            buttonText.innerHTML = '<span class="status-indicator status-processing"></span>测试中...';

            const startTime = Date.now();

            try {
                const endpoint = testType === 'deep' ? '/api/deep-search' : '/api/global-search';
                const requestBody = testType === 'deep' 
                    ? { query, tools: mockTools, useOptimized: true }
                    : { query, useOptimized: true };

                const response = await fetch(endpoint, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'X-API-Key': apiKey
                    },
                    body: JSON.stringify(requestBody)
                });

                const result = await response.json();
                const endTime = Date.now();
                const duration = endTime - startTime;

                // 显示结果
                displayTestResult(result, duration, '优化版（修复后）');

                // 更新修复状态
                if (result.success && result.data.recommendedTools && result.data.recommendedTools.length > 0) {
                    document.getElementById('parseFixStatus').innerHTML = '<span class="status-indicator status-success"></span>✅ 解析正常';
                } else {
                    document.getElementById('parseFixStatus').innerHTML = '<span class="status-indicator status-error"></span>❌ 仍有问题';
                }

                if (duration < 45000) { // 45秒内完成
                    document.getElementById('timeoutFixStatus').innerHTML = '<span class="status-indicator status-success"></span>✅ 超时处理正常';
                } else {
                    document.getElementById('timeoutFixStatus').innerHTML = '<span class="status-indicator status-error"></span>❌ 仍有超时问题';
                }

            } catch (error) {
                console.error('测试失败:', error);
                displayTestError(error.message);
            } finally {
                button.disabled = false;
                buttonText.innerHTML = '测试修复后的优化版';
            }
        }

        // 测试原版搜索（对比）
        async function testOriginalSearch() {
            const apiKey = document.getElementById('apiKey').value;
            const query = document.getElementById('testQuery').value;
            const testType = document.getElementById('testType').value;

            if (!apiKey || !query) {
                alert('请输入API密钥和搜索查询');
                return;
            }

            const button = document.querySelector('button[onclick="testOriginalSearch()"]');
            const buttonText = document.getElementById('originalButtonText');
            
            button.disabled = true;
            buttonText.innerHTML = '<span class="status-indicator status-processing"></span>测试中...';

            const startTime = Date.now();

            try {
                const endpoint = testType === 'deep' ? '/api/deep-search' : '/api/global-search';
                const requestBody = testType === 'deep' 
                    ? { query, tools: mockTools, useOptimized: false }
                    : { query, useOptimized: false };

                const response = await fetch(endpoint, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'X-API-Key': apiKey
                    },
                    body: JSON.stringify(requestBody)
                });

                const result = await response.json();
                const endTime = Date.now();
                const duration = endTime - startTime;

                // 显示结果
                displayTestResult(result, duration, '原版（对比）');

            } catch (error) {
                console.error('原版测试失败:', error);
                displayTestError(error.message);
            } finally {
                button.disabled = false;
                buttonText.innerHTML = '测试原版（对比）';
            }
        }

        // 超时测试
        async function testWithTimeout(timeout) {
            const apiKey = document.getElementById('apiKey').value;
            if (!apiKey) {
                alert('请输入API密钥');
                return;
            }

            // 临时更新配置
            try {
                await fetch('/api/search-config', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'X-API-Key': apiKey
                    },
                    body: JSON.stringify({
                        config: {
                            globalSearch: { timeout: timeout }
                        }
                    })
                });

                // 执行测试
                document.getElementById('testQuery').value = '复杂的全网搜索查询';
                document.getElementById('testType').value = 'global';
                await testOptimizedSearchFixed();

            } catch (error) {
                console.error('超时测试失败:', error);
            }
        }

        // 显示测试结果
        function displayTestResult(result, duration, version) {
            const resultsDiv = document.getElementById('testResults');
            const contentDiv = document.getElementById('testResultsContent');
            
            resultsDiv.style.display = 'block';
            
            if (result.success) {
                contentDiv.innerHTML += `
                    <div style="border: 1px solid #dee2e6; padding: 15px; margin: 10px 0; border-radius: 4px;">
                        <h5><span class="status-indicator status-success"></span>${version} - 测试成功</h5>
                        <p><strong>响应时间:</strong> ${duration}ms</p>
                        <p><strong>搜索总结:</strong> ${result.data.searchSummary}</p>
                        <p><strong>推荐工具数:</strong> ${result.data.recommendedTools?.length || 0}</p>
                        <p><strong>搜索洞察:</strong> ${result.data.searchInsights}</p>
                        ${result.data.status ? `<p><strong>状态:</strong> ${result.data.status}</p>` : ''}
                        <details>
                            <summary>完整响应数据</summary>
                            <pre>${JSON.stringify(result, null, 2)}</pre>
                        </details>
                    </div>
                `;
            } else {
                contentDiv.innerHTML += `
                    <div style="border: 1px solid #f5c6cb; padding: 15px; margin: 10px 0; border-radius: 4px; background: #f8d7da;">
                        <h5><span class="status-indicator status-error"></span>${version} - 测试失败</h5>
                        <p><strong>错误:</strong> ${result.error || '未知错误'}</p>
                        <p><strong>响应时间:</strong> ${duration}ms</p>
                    </div>
                `;
            }
        }

        // 显示测试错误
        function displayTestError(errorMessage) {
            const resultsDiv = document.getElementById('testResults');
            const contentDiv = document.getElementById('testResultsContent');
            
            resultsDiv.style.display = 'block';
            contentDiv.innerHTML += `
                <div style="border: 1px solid #f5c6cb; padding: 15px; margin: 10px 0; border-radius: 4px; background: #f8d7da;">
                    <h5><span class="status-indicator status-error"></span>网络错误</h5>
                    <p><strong>错误:</strong> ${errorMessage}</p>
                </div>
            `;
        }

        // 清空测试结果
        function clearTestResults() {
            document.getElementById('testResults').style.display = 'none';
            document.getElementById('testResultsContent').innerHTML = '';
            document.getElementById('timeoutResults').style.display = 'none';
            document.getElementById('timeoutResultsContent').innerHTML = '';
            document.getElementById('retryResults').style.display = 'none';
            document.getElementById('retryResultsContent').innerHTML = '';
        }

        // 页面加载时的提示
        window.onload = function() {
            console.log('ToolMaster 搜索修复验证测试页面已加载');
            console.log('请确保您有有效的API访问密钥来测试功能');
        };
    </script>
</body>
</html>
