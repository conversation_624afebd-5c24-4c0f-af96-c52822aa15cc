# 🗄️ ToolMaster SQL目录

本目录包含 ToolMaster 项目的所有SQL脚本和数据库相关文件。

## 📋 SQL文件说明

### 🏗️ 数据库初始化
- `supabase-setup.sql` - Supabase数据库初始化脚本
  - 创建所有必要的数据表
  - 设置索引和约束
  - 配置RLS（行级安全）策略
  - 创建必要的函数和触发器

## 📊 数据库结构

### 核心表结构
1. **tools** - 工具主表
   - 存储工具的基本信息
   - 包含名称、URL、描述、标签等字段

2. **categories** - 分类配置表
   - 存储分类结构数据（JSONB格式）
   - 支持三级分类结构

3. **operation_logs** - 操作日志表
   - 记录所有系统操作
   - 支持操作审计和追踪

4. **import_tasks** - 导入任务表
   - 管理批量导入任务
   - 跟踪导入进度和状态

### 扩展表结构
- **import_task_items** - 导入任务项目表
- **cleanup_reports** - 清理报告表
- **ai_analysis_cache** - AI分析缓存表
- **data_statistics** - 数据统计表

## 🚀 使用说明

### 初始化数据库
1. 登录到 Supabase 控制台
2. 进入 SQL Editor
3. 复制并执行 `supabase-setup.sql` 中的内容
4. 验证所有表和索引创建成功

### 数据库迁移
如需修改数据库结构：
1. 创建新的迁移脚本
2. 使用版本号命名：`migration_v1.1.0.sql`
3. 在生产环境执行前先在测试环境验证

## 📝 SQL规范

### 新增SQL文件
所有新的SQL文件都应该放在此目录下：
- 初始化脚本：`setup-*.sql`
- 迁移脚本：`migration_v*.sql`
- 数据脚本：`data-*.sql`
- 维护脚本：`maintenance-*.sql`

### SQL编写规范
1. 使用大写关键字（CREATE, SELECT, INSERT等）
2. 表名和字段名使用小写和下划线
3. 添加适当的注释说明
4. 包含错误处理（IF NOT EXISTS等）
5. 使用事务确保数据一致性

### 安全注意事项
1. 所有表都应配置适当的RLS策略
2. 敏感数据字段应加密存储
3. 定期备份重要数据
4. 限制数据库访问权限

---

**最后更新**: 2025-08-01  
**维护者**: ToolMaster 开发团队
