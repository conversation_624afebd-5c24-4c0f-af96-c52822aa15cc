-- ToolMaster 存储监控 SQL 函数修复脚本
-- 修复返回类型和变量名冲突问题

-- ===========================================
-- 1. 修复数据库大小函数
-- ===========================================
CREATE OR REPLACE FUNCTION get_database_size()
RETURNS TABLE(size text, size_bytes bigint)
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
  total_size bigint;
BEGIN
  SELECT sum(pg_database_size(pg_database.datname)) INTO total_size FROM pg_database;
  
  RETURN QUERY
  SELECT 
    pg_size_pretty(total_size) as size,
    total_size as size_bytes;
END;
$$;

-- ===========================================
-- 2. 修复 WAL 大小函数
-- ===========================================
CREATE OR REPLACE FUNCTION get_wal_size()
RETURNS TABLE(size text, size_bytes bigint)
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
  total_wal_size bigint;
BEGIN
  BEGIN
    SELECT sum(size) INTO total_wal_size FROM pg_ls_waldir();
    
    RETURN QUERY
    SELECT 
      pg_size_pretty(total_wal_size) as size,
      total_wal_size as size_bytes;
  EXCEPTION
    WHEN OTHERS THEN
      -- 如果无法访问 WAL 目录，返回默认值
      RETURN QUERY
      SELECT '未知'::text as size, 0::bigint as size_bytes;
  END;
END;
$$;

-- ===========================================
-- 3. 修复表大小函数
-- ===========================================
CREATE OR REPLACE FUNCTION get_table_sizes()
RETURNS TABLE(
  table_name text,
  schema_name text,
  size text,
  size_bytes bigint,
  row_count bigint,
  percentage numeric
)
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
  total_size_val bigint := 0;
  rec record;
BEGIN
  -- 创建临时表存储结果
  CREATE TEMP TABLE IF NOT EXISTS temp_table_sizes (
    table_name text,
    schema_name text,
    size text,
    size_bytes bigint,
    row_count bigint,
    percentage numeric
  );
  
  -- 清空临时表
  DELETE FROM temp_table_sizes;

  -- 获取所有公共表的大小
  FOR rec IN 
    SELECT 
      t.tablename as tbl_name,
      t.schemaname as sch_name,
      pg_total_relation_size(t.schemaname||'.'||t.tablename) as tbl_bytes,
      pg_size_pretty(pg_total_relation_size(t.schemaname||'.'||t.tablename)) as tbl_size
    FROM pg_tables t 
    WHERE t.schemaname = 'public'
    ORDER BY pg_total_relation_size(t.schemaname||'.'||t.tablename) DESC
  LOOP
    -- 获取行数
    DECLARE
      row_count_val bigint;
      sql_text text;
    BEGIN
      sql_text := 'SELECT COUNT(*) FROM ' || quote_ident(rec.sch_name) || '.' || quote_ident(rec.tbl_name);
      EXECUTE sql_text INTO row_count_val;
      
      -- 插入临时表
      INSERT INTO temp_table_sizes VALUES (
        rec.tbl_name,
        rec.sch_name,
        rec.tbl_size,
        rec.tbl_bytes,
        row_count_val,
        0 -- 百分比稍后计算
      );
      
      total_size_val := total_size_val + rec.tbl_bytes;
    EXCEPTION
      WHEN OTHERS THEN
        -- 如果无法获取行数，使用 0
        INSERT INTO temp_table_sizes VALUES (
          rec.tbl_name,
          rec.sch_name,
          rec.tbl_size,
          rec.tbl_bytes,
          0,
          0
        );
        total_size_val := total_size_val + rec.tbl_bytes;
    END;
  END LOOP;

  -- 更新百分比
  IF total_size_val > 0 THEN
    UPDATE temp_table_sizes 
    SET percentage = ROUND((size_bytes::numeric / total_size_val::numeric) * 100, 2);
  END IF;

  -- 返回结果
  RETURN QUERY
  SELECT * FROM temp_table_sizes
  ORDER BY size_bytes DESC;

  -- 清理临时表
  DROP TABLE IF EXISTS temp_table_sizes;
END;
$$;

-- ===========================================
-- 4. 重新授权
-- ===========================================
GRANT EXECUTE ON FUNCTION get_database_size() TO anon;
GRANT EXECUTE ON FUNCTION get_wal_size() TO anon;
GRANT EXECUTE ON FUNCTION get_table_sizes() TO anon;

GRANT EXECUTE ON FUNCTION get_database_size() TO authenticated;
GRANT EXECUTE ON FUNCTION get_wal_size() TO authenticated;
GRANT EXECUTE ON FUNCTION get_table_sizes() TO authenticated;

-- ===========================================
-- 5. 测试函数
-- ===========================================
-- 取消注释以下行来测试函数
-- SELECT * FROM get_database_size();
-- SELECT * FROM get_wal_size();
-- SELECT * FROM get_table_sizes();
