-- AI API 使用监控表结构
-- 用于监控 DeepSeek API 的使用情况和费用

-- 1. AI 使用详细日志表
CREATE TABLE IF NOT EXISTS ai_usage_logs (
    id SERIAL PRIMARY KEY,

    -- 基本信息
    api_type VARCHAR(50) NOT NULL, -- 'deep-search', 'global-search', 'analyze-url', 'add-tool'
    model VARCHAR(50) NOT NULL DEFAULT 'deepseek-chat', -- AI 模型名称

    -- Token 使用情况
    input_tokens INTEGER NOT NULL DEFAULT 0, -- 输入 tokens
    output_tokens INTEGER NOT NULL DEFAULT 0, -- 输出 tokens
    total_tokens INTEGER NOT NULL DEFAULT 0, -- 总 tokens

    -- 费用相关
    cost_yuan DECIMAL(10,4) NOT NULL DEFAULT 0, -- 费用（元）
    is_cache_hit BOOLEAN NOT NULL DEFAULT FALSE, -- 是否缓存命中
    is_discount_period BOOLEAN NOT NULL DEFAULT FALSE, -- 是否优惠时段

    -- 性能指标
    response_time_ms INTEGER NOT NULL DEFAULT 0, -- 响应时间（毫秒）
    success BOOLEAN NOT NULL DEFAULT TRUE, -- 是否成功
    error_message TEXT, -- 错误信息

    -- 请求详情
    request_id VARCHAR(100), -- 请求ID（用于追踪）
    user_query TEXT, -- 用户查询内容（脱敏）

    -- 时间戳
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- 2. AI 使用每日汇总表
CREATE TABLE IF NOT EXISTS ai_usage_daily_summary (
    id SERIAL PRIMARY KEY,
    date_only DATE NOT NULL UNIQUE,

    -- 调用统计
    total_calls INTEGER NOT NULL DEFAULT 0,
    successful_calls INTEGER NOT NULL DEFAULT 0,
    failed_calls INTEGER NOT NULL DEFAULT 0,
    success_rate DECIMAL(5,2) NOT NULL DEFAULT 0, -- 成功率百分比

    -- Token 统计
    total_input_tokens BIGINT NOT NULL DEFAULT 0,
    total_output_tokens BIGINT NOT NULL DEFAULT 0,
    total_tokens BIGINT NOT NULL DEFAULT 0,

    -- 费用统计
    total_cost_yuan DECIMAL(10,4) NOT NULL DEFAULT 0,
    cache_hit_cost_yuan DECIMAL(10,4) NOT NULL DEFAULT 0, -- 缓存命中费用
    discount_cost_yuan DECIMAL(10,4) NOT NULL DEFAULT 0, -- 优惠时段费用

    -- 性能统计
    avg_response_time_ms INTEGER NOT NULL DEFAULT 0,
    max_response_time_ms INTEGER NOT NULL DEFAULT 0,

    -- 按 API 类型分组统计（JSON 格式）
    api_type_stats JSONB DEFAULT '{}',

    -- 时间戳
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- 3. AI 使用每月汇总表
CREATE TABLE IF NOT EXISTS ai_usage_monthly_summary (
    id SERIAL PRIMARY KEY,
    year_month VARCHAR(7) NOT NULL UNIQUE, -- 格式：2025-08

    -- 调用统计
    total_calls INTEGER NOT NULL DEFAULT 0,
    successful_calls INTEGER NOT NULL DEFAULT 0,
    failed_calls INTEGER NOT NULL DEFAULT 0,
    success_rate DECIMAL(5,2) NOT NULL DEFAULT 0,

    -- Token 统计
    total_input_tokens BIGINT NOT NULL DEFAULT 0,
    total_output_tokens BIGINT NOT NULL DEFAULT 0,
    total_tokens BIGINT NOT NULL DEFAULT 0,

    -- 费用统计
    total_cost_yuan DECIMAL(10,4) NOT NULL DEFAULT 0,
    avg_daily_cost_yuan DECIMAL(10,4) NOT NULL DEFAULT 0,

    -- 按日期的费用分布（JSON 格式）
    daily_cost_distribution JSONB DEFAULT '{}',

    -- 按 API 类型分组统计（JSON 格式）
    api_type_stats JSONB DEFAULT '{}',

    -- 时间戳
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- 创建索引以提高查询性能
CREATE INDEX IF NOT EXISTS idx_ai_usage_logs_api_type ON ai_usage_logs(api_type);
CREATE INDEX IF NOT EXISTS idx_ai_usage_logs_created_at ON ai_usage_logs(created_at);
CREATE INDEX IF NOT EXISTS idx_ai_usage_logs_success ON ai_usage_logs(success);
CREATE INDEX IF NOT EXISTS idx_ai_usage_logs_date_created ON ai_usage_logs(DATE(created_at));

-- 创建触发器自动更新 updated_at 字段
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = CURRENT_TIMESTAMP;
    RETURN NEW;
END;
$$ language 'plpgsql';

CREATE TRIGGER update_ai_usage_daily_summary_updated_at
    BEFORE UPDATE ON ai_usage_daily_summary
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_ai_usage_monthly_summary_updated_at
    BEFORE UPDATE ON ai_usage_monthly_summary
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- 插入初始数据（当前日期和月份的记录）
INSERT INTO ai_usage_daily_summary (date_only)
VALUES (CURRENT_DATE)
ON CONFLICT (date_only) DO NOTHING;

INSERT INTO ai_usage_monthly_summary (year_month)
VALUES (TO_CHAR(CURRENT_DATE, 'YYYY-MM'))
ON CONFLICT (year_month) DO NOTHING;

-- 添加注释
COMMENT ON TABLE ai_usage_logs IS 'AI API 使用详细日志，记录每次 API 调用的详细信息';
COMMENT ON TABLE ai_usage_daily_summary IS 'AI API 使用每日汇总统计';
COMMENT ON TABLE ai_usage_monthly_summary IS 'AI API 使用每月汇总统计';

COMMENT ON COLUMN ai_usage_logs.api_type IS 'API 类型：deep-search, global-search, analyze-url, add-tool';
COMMENT ON COLUMN ai_usage_logs.cost_yuan IS '本次调用费用（人民币元）';
COMMENT ON COLUMN ai_usage_logs.is_cache_hit IS '是否命中缓存（影响费用计算）';
COMMENT ON COLUMN ai_usage_logs.is_discount_period IS '是否在优惠时段（00:30-08:30）';

-- ========================================
-- 示例数据插入（可选，用于测试）
-- ========================================

-- 插入一些测试数据到 AI 使用日志表
-- INSERT INTO ai_usage_logs (
--     api_type, model, input_tokens, output_tokens, total_tokens,
--     cost_yuan, is_cache_hit, is_discount_period, response_time_ms,
--     success, request_id, user_query
-- ) VALUES
--     ('deep-search', 'deepseek-chat', 150, 80, 230, 0.0024, false, false, 1200, true, 'search-test1', '测试深度搜索'),
--     ('analyze-url', 'deepseek-chat', 200, 120, 320, 0.0032, false, true, 800, true, 'analyze-test1', '分析网站URL'),
--     ('add-tool', 'deepseek-chat', 180, 100, 280, 0.0028, true, false, 950, true, 'add-test1', '添加新工具'),
--     ('global-search', 'deepseek-chat', 220, 150, 370, 0.0042, false, false, 1500, true, 'global-test1', '全网搜索工具'),
--     ('deep-search', 'deepseek-chat', 160, 90, 250, 0.0026, false, true, 1100, false, 'search-test2', '另一个深度搜索');

-- 插入今日汇总数据（示例）
-- INSERT INTO ai_usage_daily_summary (
--     date_only, total_calls, successful_calls, failed_calls, success_rate,
--     total_input_tokens, total_output_tokens, total_tokens,
--     total_cost_yuan, cache_hit_cost_yuan, discount_cost_yuan,
--     avg_response_time_ms, max_response_time_ms,
--     api_type_stats
-- ) VALUES (
--     CURRENT_DATE, 5, 4, 1, 80.00,
--     910, 540, 1450,
--     0.0152, 0.0028, 0.0052,
--     1110, 1500,
--     '{
--         "deep-search": {"calls": 2, "tokens": 480, "cost": 0.0050, "success_rate": 50.00},
--         "analyze-url": {"calls": 1, "tokens": 320, "cost": 0.0032, "success_rate": 100.00},
--         "add-tool": {"calls": 1, "tokens": 280, "cost": 0.0028, "success_rate": 100.00},
--         "global-search": {"calls": 1, "tokens": 370, "cost": 0.0042, "success_rate": 100.00}
--     }'
-- ) ON CONFLICT (date_only) DO UPDATE SET
--     total_calls = EXCLUDED.total_calls,
--     successful_calls = EXCLUDED.successful_calls,
--     failed_calls = EXCLUDED.failed_calls,
--     success_rate = EXCLUDED.success_rate,
--     total_input_tokens = EXCLUDED.total_input_tokens,
--     total_output_tokens = EXCLUDED.total_output_tokens,
--     total_tokens = EXCLUDED.total_tokens,
--     total_cost_yuan = EXCLUDED.total_cost_yuan,
--     cache_hit_cost_yuan = EXCLUDED.cache_hit_cost_yuan,
--     discount_cost_yuan = EXCLUDED.discount_cost_yuan,
--     avg_response_time_ms = EXCLUDED.avg_response_time_ms,
--     max_response_time_ms = EXCLUDED.max_response_time_ms,
--     api_type_stats = EXCLUDED.api_type_stats,
--     updated_at = CURRENT_TIMESTAMP;

-- 插入本月汇总数据（示例）
-- INSERT INTO ai_usage_monthly_summary (
--     year_month, total_calls, successful_calls, failed_calls, success_rate,
--     total_input_tokens, total_output_tokens, total_tokens,
--     total_cost_yuan, avg_daily_cost_yuan,
--     daily_cost_distribution, api_type_stats
-- ) VALUES (
--     TO_CHAR(CURRENT_DATE, 'YYYY-MM'), 15, 12, 3, 80.00,
--     2730, 1620, 4350,
--     0.0456, 0.0152,
--     '{}',
--     '{
--         "deep-search": {"calls": 6, "tokens": 1440, "cost": 0.0150, "success_rate": 66.67},
--         "analyze-url": {"calls": 3, "tokens": 960, "cost": 0.0096, "success_rate": 100.00},
--         "add-tool": {"calls": 3, "tokens": 840, "cost": 0.0084, "success_rate": 100.00},
--         "global-search": {"calls": 3, "tokens": 1110, "cost": 0.0126, "success_rate": 100.00}
--     }'
-- ) ON CONFLICT (year_month) DO UPDATE SET
--     total_calls = EXCLUDED.total_calls,
--     successful_calls = EXCLUDED.successful_calls,
--     failed_calls = EXCLUDED.failed_calls,
--     success_rate = EXCLUDED.success_rate,
--     total_input_tokens = EXCLUDED.total_input_tokens,
--     total_output_tokens = EXCLUDED.total_output_tokens,
--     total_tokens = EXCLUDED.total_tokens,
--     total_cost_yuan = EXCLUDED.total_cost_yuan,
--     avg_daily_cost_yuan = EXCLUDED.avg_daily_cost_yuan,
--     daily_cost_distribution = EXCLUDED.daily_cost_distribution,
--     api_type_stats = EXCLUDED.api_type_stats,
--     updated_at = CURRENT_TIMESTAMP;
