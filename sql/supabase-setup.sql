-- ToolMaster Supabase 数据库初始化脚本
-- 在 Supabase SQL Editor 中执行此脚本

-- ===========================================
-- 1. 创建工具表 (tools)
-- ===========================================
CREATE TABLE IF NOT EXISTS public.tools (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    name TEXT NOT NULL,
    url TEXT NOT NULL,
    description TEXT DEFAULT '',
    tags TEXT[] DEFAULT '{}',
    category TEXT DEFAULT '',
    subcategory TEXT DEFAULT '',
    subsubcategory TEXT DEFAULT '',
    added_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    sensitive BOOLEAN DEFAULT FALSE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- ===========================================
-- 2. 创建分类表 (categories)
-- ===========================================
CREATE TABLE IF NOT EXISTS public.categories (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    data JSONB NOT NULL DEFAULT '[]',
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- ===========================================
-- 3. 创建操作日志表 (operation_logs)
-- ===========================================
CREATE TABLE IF NOT EXISTS public.operation_logs (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    action TEXT NOT NULL,
    operation_type TEXT NOT NULL,
    target_type TEXT,
    target_id TEXT,
    details JSONB DEFAULT '{}',
    status TEXT DEFAULT 'success',
    error_message TEXT,
    ip_address TEXT,
    user_agent TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- ===========================================
-- 4. 创建导入任务表 (import_tasks)
-- ===========================================
CREATE TABLE IF NOT EXISTS public.import_tasks (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    task_name TEXT NOT NULL,
    file_name TEXT,
    file_size INTEGER,
    file_type TEXT,
    content_preview TEXT,
    status TEXT DEFAULT 'pending',
    total_items INTEGER DEFAULT 0,
    processed_items INTEGER DEFAULT 0,
    success_items INTEGER DEFAULT 0,
    failed_items INTEGER DEFAULT 0,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    completed_at TIMESTAMP WITH TIME ZONE
);

-- ===========================================
-- 5. 创建导入任务项表 (import_task_items)
-- ===========================================
CREATE TABLE IF NOT EXISTS public.import_task_items (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    task_id UUID REFERENCES public.import_tasks(id) ON DELETE CASCADE,
    item_index INTEGER NOT NULL,
    original_data JSONB NOT NULL,
    processed_data JSONB,
    tool_id UUID,
    status TEXT DEFAULT 'pending',
    error_message TEXT,
    ai_analysis_result JSONB,
    processing_time_ms INTEGER,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- ===========================================
-- 6. 创建清理报告表 (cleanup_reports)
-- ===========================================
CREATE TABLE IF NOT EXISTS public.cleanup_reports (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    operation_type TEXT NOT NULL,
    original_count INTEGER NOT NULL,
    processed_count INTEGER NOT NULL,
    removed_count INTEGER NOT NULL,
    details JSONB DEFAULT '{}',
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- ===========================================
-- 7. 创建AI分析缓存表 (ai_analysis_cache)
-- ===========================================
CREATE TABLE IF NOT EXISTS public.ai_analysis_cache (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    url_hash TEXT UNIQUE NOT NULL,
    original_url TEXT NOT NULL,
    analysis_result JSONB NOT NULL,
    confidence_score DECIMAL(3,2) DEFAULT 0.8,
    processing_time_ms INTEGER,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    expires_at TIMESTAMP WITH TIME ZONE DEFAULT (NOW() + INTERVAL '7 days')
);

-- ===========================================
-- 8. 创建数据统计表 (data_statistics)
-- ===========================================
CREATE TABLE IF NOT EXISTS public.data_statistics (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    stat_type TEXT NOT NULL,
    stat_key TEXT NOT NULL,
    stat_value JSONB NOT NULL,
    calculated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    expires_at TIMESTAMP WITH TIME ZONE,
    UNIQUE(stat_type, stat_key)
);

-- ===========================================
-- 9. 创建索引以提高查询性能
-- ===========================================
-- 工具表索引
CREATE INDEX IF NOT EXISTS idx_tools_category ON public.tools(category);
CREATE INDEX IF NOT EXISTS idx_tools_created_at ON public.tools(created_at);
CREATE INDEX IF NOT EXISTS idx_tools_url ON public.tools(url);
CREATE INDEX IF NOT EXISTS idx_tools_tags ON public.tools USING GIN(tags);

-- 操作日志索引
CREATE INDEX IF NOT EXISTS idx_operation_logs_created_at ON public.operation_logs(created_at);
CREATE INDEX IF NOT EXISTS idx_operation_logs_action ON public.operation_logs(action);

-- 导入任务索引
CREATE INDEX IF NOT EXISTS idx_import_tasks_status ON public.import_tasks(status);
CREATE INDEX IF NOT EXISTS idx_import_task_items_task_id ON public.import_task_items(task_id);

-- AI缓存索引
CREATE INDEX IF NOT EXISTS idx_ai_cache_url_hash ON public.ai_analysis_cache(url_hash);
CREATE INDEX IF NOT EXISTS idx_ai_cache_expires_at ON public.ai_analysis_cache(expires_at);

-- 统计数据索引
CREATE INDEX IF NOT EXISTS idx_data_statistics_type_key ON public.data_statistics(stat_type, stat_key);

-- ===========================================
-- 10. 启用行级安全 (RLS) - 可选
-- ===========================================
-- 如果需要更严格的安全控制，可以启用 RLS
-- ALTER TABLE public.tools ENABLE ROW LEVEL SECURITY;
-- ALTER TABLE public.categories ENABLE ROW LEVEL SECURITY;

-- ===========================================
-- 11. 插入初始分类数据
-- ===========================================
INSERT INTO public.categories (data) VALUES ('[]') 
ON CONFLICT DO NOTHING;

-- ===========================================
-- 完成提示
-- ===========================================
-- 数据库初始化完成！
-- 现在可以在 ToolMaster 应用中正常使用所有功能了。
